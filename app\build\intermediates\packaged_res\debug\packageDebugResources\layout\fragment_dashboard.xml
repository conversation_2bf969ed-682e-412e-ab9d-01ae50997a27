<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/daily_macros"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Calories Card -->
        <com.google.android.material.card.MaterialCardView
            style="@style/MacroCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/calories"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/calories_color" />

                <ProgressBar
                    android:id="@+id/calories_progress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:progressTint="@color/calories_color" />

                <TextView
                    android:id="@+id/calories_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAlignment="center"
                    tools:text="1500 / 2000 kcal" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Protein Card -->
        <com.google.android.material.card.MaterialCardView
            style="@style/MacroCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/protein"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/protein_color" />

                <ProgressBar
                    android:id="@+id/protein_progress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:progressTint="@color/protein_color" />

                <TextView
                    android:id="@+id/protein_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAlignment="center"
                    tools:text="120g / 150g" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Carbs Card -->
        <com.google.android.material.card.MaterialCardView
            style="@style/MacroCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/carbs"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/carbs_color" />

                <ProgressBar
                    android:id="@+id/carbs_progress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:progressTint="@color/carbs_color" />

                <TextView
                    android:id="@+id/carbs_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAlignment="center"
                    tools:text="180g / 250g" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Fat Card -->
        <com.google.android.material.card.MaterialCardView
            style="@style/MacroCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/fat"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/fat_color" />

                <ProgressBar
                    android:id="@+id/fat_progress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:progressTint="@color/fat_color" />

                <TextView
                    android:id="@+id/fat_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAlignment="center"
                    tools:text="45g / 65g" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <Button
            android:id="@+id/refresh_button"
            style="@style/MacroButtonOutlined"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Refresh" />

    </LinearLayout>

</ScrollView>
