var id=e=>{throw TypeError(e)};var _a=(e,t,n)=>t.has(e)||id("Cannot "+n);var T=(e,t,n)=>(_a(e,t,"read from private field"),n?n.call(e):t.get(e)),Z=(e,t,n)=>t.has(e)?id("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),H=(e,t,n,r)=>(_a(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Pe=(e,t,n)=>(_a(e,t,"access private method"),n);var xi=(e,t,n,r)=>({set _(o){H(e,t,o,n)},get _(){return T(e,t,r)}});function Ny(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function gp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var yp={exports:{}},Hs={},wp={exports:{}},G={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ui=Symbol.for("react.element"),Ty=Symbol.for("react.portal"),Ry=Symbol.for("react.fragment"),_y=Symbol.for("react.strict_mode"),Oy=Symbol.for("react.profiler"),Ay=Symbol.for("react.provider"),jy=Symbol.for("react.context"),My=Symbol.for("react.forward_ref"),Iy=Symbol.for("react.suspense"),Dy=Symbol.for("react.memo"),Ly=Symbol.for("react.lazy"),sd=Symbol.iterator;function Fy(e){return e===null||typeof e!="object"?null:(e=sd&&e[sd]||e["@@iterator"],typeof e=="function"?e:null)}var xp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Sp=Object.assign,Ep={};function io(e,t,n){this.props=e,this.context=t,this.refs=Ep,this.updater=n||xp}io.prototype.isReactComponent={};io.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};io.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Cp(){}Cp.prototype=io.prototype;function Au(e,t,n){this.props=e,this.context=t,this.refs=Ep,this.updater=n||xp}var ju=Au.prototype=new Cp;ju.constructor=Au;Sp(ju,io.prototype);ju.isPureReactComponent=!0;var ad=Array.isArray,bp=Object.prototype.hasOwnProperty,Mu={current:null},kp={key:!0,ref:!0,__self:!0,__source:!0};function Pp(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)bp.call(t,r)&&!kp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:ui,type:e,key:i,ref:s,props:o,_owner:Mu.current}}function zy(e,t){return{$$typeof:ui,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Iu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ui}function $y(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ld=/\/+/g;function Oa(e,t){return typeof e=="object"&&e!==null&&e.key!=null?$y(""+e.key):t.toString(36)}function Ki(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ui:case Ty:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Oa(s,0):r,ad(o)?(n="",e!=null&&(n=e.replace(ld,"$&/")+"/"),Ki(o,t,n,"",function(u){return u})):o!=null&&(Iu(o)&&(o=zy(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(ld,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",ad(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Oa(i,a);s+=Ki(i,t,n,l,o)}else if(l=Fy(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Oa(i,a++),s+=Ki(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Si(e,t,n){if(e==null)return e;var r=[],o=0;return Ki(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Uy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Le={current:null},Gi={transition:null},By={ReactCurrentDispatcher:Le,ReactCurrentBatchConfig:Gi,ReactCurrentOwner:Mu};function Np(){throw Error("act(...) is not supported in production builds of React.")}G.Children={map:Si,forEach:function(e,t,n){Si(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Si(e,function(){t++}),t},toArray:function(e){return Si(e,function(t){return t})||[]},only:function(e){if(!Iu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};G.Component=io;G.Fragment=Ry;G.Profiler=Oy;G.PureComponent=Au;G.StrictMode=_y;G.Suspense=Iy;G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=By;G.act=Np;G.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Sp({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Mu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)bp.call(t,l)&&!kp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ui,type:e.type,key:o,ref:i,props:r,_owner:s}};G.createContext=function(e){return e={$$typeof:jy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ay,_context:e},e.Consumer=e};G.createElement=Pp;G.createFactory=function(e){var t=Pp.bind(null,e);return t.type=e,t};G.createRef=function(){return{current:null}};G.forwardRef=function(e){return{$$typeof:My,render:e}};G.isValidElement=Iu;G.lazy=function(e){return{$$typeof:Ly,_payload:{_status:-1,_result:e},_init:Uy}};G.memo=function(e,t){return{$$typeof:Dy,type:e,compare:t===void 0?null:t}};G.startTransition=function(e){var t=Gi.transition;Gi.transition={};try{e()}finally{Gi.transition=t}};G.unstable_act=Np;G.useCallback=function(e,t){return Le.current.useCallback(e,t)};G.useContext=function(e){return Le.current.useContext(e)};G.useDebugValue=function(){};G.useDeferredValue=function(e){return Le.current.useDeferredValue(e)};G.useEffect=function(e,t){return Le.current.useEffect(e,t)};G.useId=function(){return Le.current.useId()};G.useImperativeHandle=function(e,t,n){return Le.current.useImperativeHandle(e,t,n)};G.useInsertionEffect=function(e,t){return Le.current.useInsertionEffect(e,t)};G.useLayoutEffect=function(e,t){return Le.current.useLayoutEffect(e,t)};G.useMemo=function(e,t){return Le.current.useMemo(e,t)};G.useReducer=function(e,t,n){return Le.current.useReducer(e,t,n)};G.useRef=function(e){return Le.current.useRef(e)};G.useState=function(e){return Le.current.useState(e)};G.useSyncExternalStore=function(e,t,n){return Le.current.useSyncExternalStore(e,t,n)};G.useTransition=function(){return Le.current.useTransition()};G.version="18.3.1";wp.exports=G;var h=wp.exports;const O=gp(h),Tp=Ny({__proto__:null,default:O},[h]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vy=h,Wy=Symbol.for("react.element"),Hy=Symbol.for("react.fragment"),Qy=Object.prototype.hasOwnProperty,Ky=Vy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Gy={key:!0,ref:!0,__self:!0,__source:!0};function Rp(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Qy.call(t,r)&&!Gy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Wy,type:e,key:i,ref:s,props:o,_owner:Ky.current}}Hs.Fragment=Hy;Hs.jsx=Rp;Hs.jsxs=Rp;yp.exports=Hs;var g=yp.exports,_p={exports:{}},tt={},Op={exports:{}},Ap={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(b,_){var F=b.length;b.push(_);e:for(;0<F;){var D=F-1>>>1,U=b[D];if(0<o(U,_))b[D]=_,b[F]=U,F=D;else break e}}function n(b){return b.length===0?null:b[0]}function r(b){if(b.length===0)return null;var _=b[0],F=b.pop();if(F!==_){b[0]=F;e:for(var D=0,U=b.length,Y=U>>>1;D<Y;){var ce=2*(D+1)-1,He=b[ce],J=ce+1,ct=b[J];if(0>o(He,F))J<U&&0>o(ct,He)?(b[D]=ct,b[J]=F,D=J):(b[D]=He,b[ce]=F,D=ce);else if(J<U&&0>o(ct,F))b[D]=ct,b[J]=F,D=J;else break e}}return _}function o(b,_){var F=b.sortIndex-_.sortIndex;return F!==0?F:b.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],f=1,d=null,c=3,y=!1,x=!1,p=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,v=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function w(b){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=b)r(u),_.sortIndex=_.expirationTime,t(l,_);else break;_=n(u)}}function E(b){if(p=!1,w(b),!x)if(n(l)!==null)x=!0,z(C);else{var _=n(u);_!==null&&K(E,_.startTime-b)}}function C(b,_){x=!1,p&&(p=!1,m(P),P=-1),y=!0;var F=c;try{for(w(_),d=n(l);d!==null&&(!(d.expirationTime>_)||b&&!$());){var D=d.callback;if(typeof D=="function"){d.callback=null,c=d.priorityLevel;var U=D(d.expirationTime<=_);_=e.unstable_now(),typeof U=="function"?d.callback=U:d===n(l)&&r(l),w(_)}else r(l);d=n(l)}if(d!==null)var Y=!0;else{var ce=n(u);ce!==null&&K(E,ce.startTime-_),Y=!1}return Y}finally{d=null,c=F,y=!1}}var k=!1,N=null,P=-1,M=5,A=-1;function $(){return!(e.unstable_now()-A<M)}function L(){if(N!==null){var b=e.unstable_now();A=b;var _=!0;try{_=N(!0,b)}finally{_?B():(k=!1,N=null)}}else k=!1}var B;if(typeof v=="function")B=function(){v(L)};else if(typeof MessageChannel<"u"){var j=new MessageChannel,Q=j.port2;j.port1.onmessage=L,B=function(){Q.postMessage(null)}}else B=function(){S(L,0)};function z(b){N=b,k||(k=!0,B())}function K(b,_){P=S(function(){b(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(b){b.callback=null},e.unstable_continueExecution=function(){x||y||(x=!0,z(C))},e.unstable_forceFrameRate=function(b){0>b||125<b?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<b?Math.floor(1e3/b):5},e.unstable_getCurrentPriorityLevel=function(){return c},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(b){switch(c){case 1:case 2:case 3:var _=3;break;default:_=c}var F=c;c=_;try{return b()}finally{c=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(b,_){switch(b){case 1:case 2:case 3:case 4:case 5:break;default:b=3}var F=c;c=b;try{return _()}finally{c=F}},e.unstable_scheduleCallback=function(b,_,F){var D=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?D+F:D):F=D,b){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=F+U,b={id:f++,callback:_,priorityLevel:b,startTime:F,expirationTime:U,sortIndex:-1},F>D?(b.sortIndex=F,t(u,b),n(l)===null&&b===n(u)&&(p?(m(P),P=-1):p=!0,K(E,F-D))):(b.sortIndex=U,t(l,b),x||y||(x=!0,z(C))),b},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(b){var _=c;return function(){var F=c;c=_;try{return b.apply(this,arguments)}finally{c=F}}}})(Ap);Op.exports=Ap;var Yy=Op.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xy=h,Je=Yy;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var jp=new Set,$o={};function ir(e,t){Yr(e,t),Yr(e+"Capture",t)}function Yr(e,t){for($o[e]=t,e=0;e<t.length;e++)jp.add(t[e])}var Wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yl=Object.prototype.hasOwnProperty,qy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ud={},cd={};function Zy(e){return yl.call(cd,e)?!0:yl.call(ud,e)?!1:qy.test(e)?cd[e]=!0:(ud[e]=!0,!1)}function Jy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function e0(e,t,n,r){if(t===null||typeof t>"u"||Jy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Fe(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ke={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ke[e]=new Fe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ke[t]=new Fe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ke[e]=new Fe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ke[e]=new Fe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ke[e]=new Fe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ke[e]=new Fe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ke[e]=new Fe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ke[e]=new Fe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ke[e]=new Fe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Du=/[\-:]([a-z])/g;function Lu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Du,Lu);ke[t]=new Fe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Du,Lu);ke[t]=new Fe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Du,Lu);ke[t]=new Fe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ke[e]=new Fe(e,1,!1,e.toLowerCase(),null,!1,!1)});ke.xlinkHref=new Fe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ke[e]=new Fe(e,1,!1,e.toLowerCase(),null,!0,!0)});function Fu(e,t,n,r){var o=ke.hasOwnProperty(t)?ke[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(e0(t,n,o,r)&&(n=null),r||o===null?Zy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Xt=Xy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ei=Symbol.for("react.element"),vr=Symbol.for("react.portal"),gr=Symbol.for("react.fragment"),zu=Symbol.for("react.strict_mode"),wl=Symbol.for("react.profiler"),Mp=Symbol.for("react.provider"),Ip=Symbol.for("react.context"),$u=Symbol.for("react.forward_ref"),xl=Symbol.for("react.suspense"),Sl=Symbol.for("react.suspense_list"),Uu=Symbol.for("react.memo"),un=Symbol.for("react.lazy"),Dp=Symbol.for("react.offscreen"),dd=Symbol.iterator;function po(e){return e===null||typeof e!="object"?null:(e=dd&&e[dd]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,Aa;function Co(e){if(Aa===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Aa=t&&t[1]||""}return`
`+Aa+e}var ja=!1;function Ma(e,t){if(!e||ja)return"";ja=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{ja=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Co(e):""}function t0(e){switch(e.tag){case 5:return Co(e.type);case 16:return Co("Lazy");case 13:return Co("Suspense");case 19:return Co("SuspenseList");case 0:case 2:case 15:return e=Ma(e.type,!1),e;case 11:return e=Ma(e.type.render,!1),e;case 1:return e=Ma(e.type,!0),e;default:return""}}function El(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case gr:return"Fragment";case vr:return"Portal";case wl:return"Profiler";case zu:return"StrictMode";case xl:return"Suspense";case Sl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ip:return(e.displayName||"Context")+".Consumer";case Mp:return(e._context.displayName||"Context")+".Provider";case $u:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Uu:return t=e.displayName||null,t!==null?t:El(e.type)||"Memo";case un:t=e._payload,e=e._init;try{return El(e(t))}catch{}}return null}function n0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return El(t);case 8:return t===zu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function _n(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Lp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function r0(e){var t=Lp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ci(e){e._valueTracker||(e._valueTracker=r0(e))}function Fp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Lp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function fs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Cl(e,t){var n=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function fd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=_n(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function zp(e,t){t=t.checked,t!=null&&Fu(e,"checked",t,!1)}function bl(e,t){zp(e,t);var n=_n(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?kl(e,t.type,n):t.hasOwnProperty("defaultValue")&&kl(e,t.type,_n(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function pd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function kl(e,t,n){(t!=="number"||fs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var bo=Array.isArray;function _r(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+_n(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Pl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function hd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(bo(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:_n(n)}}function $p(e,t){var n=_n(t.value),r=_n(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function md(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Up(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Nl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Up(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var bi,Bp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(bi=bi||document.createElement("div"),bi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=bi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Uo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var No={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},o0=["Webkit","ms","Moz","O"];Object.keys(No).forEach(function(e){o0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),No[t]=No[e]})});function Vp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||No.hasOwnProperty(e)&&No[e]?(""+t).trim():t+"px"}function Wp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Vp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var i0=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Tl(e,t){if(t){if(i0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Rl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _l=null;function Bu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ol=null,Or=null,Ar=null;function vd(e){if(e=fi(e)){if(typeof Ol!="function")throw Error(R(280));var t=e.stateNode;t&&(t=Xs(t),Ol(e.stateNode,e.type,t))}}function Hp(e){Or?Ar?Ar.push(e):Ar=[e]:Or=e}function Qp(){if(Or){var e=Or,t=Ar;if(Ar=Or=null,vd(e),t)for(e=0;e<t.length;e++)vd(t[e])}}function Kp(e,t){return e(t)}function Gp(){}var Ia=!1;function Yp(e,t,n){if(Ia)return e(t,n);Ia=!0;try{return Kp(e,t,n)}finally{Ia=!1,(Or!==null||Ar!==null)&&(Gp(),Qp())}}function Bo(e,t){var n=e.stateNode;if(n===null)return null;var r=Xs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Al=!1;if(Wt)try{var ho={};Object.defineProperty(ho,"passive",{get:function(){Al=!0}}),window.addEventListener("test",ho,ho),window.removeEventListener("test",ho,ho)}catch{Al=!1}function s0(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var To=!1,ps=null,hs=!1,jl=null,a0={onError:function(e){To=!0,ps=e}};function l0(e,t,n,r,o,i,s,a,l){To=!1,ps=null,s0.apply(a0,arguments)}function u0(e,t,n,r,o,i,s,a,l){if(l0.apply(this,arguments),To){if(To){var u=ps;To=!1,ps=null}else throw Error(R(198));hs||(hs=!0,jl=u)}}function sr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Xp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function gd(e){if(sr(e)!==e)throw Error(R(188))}function c0(e){var t=e.alternate;if(!t){if(t=sr(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return gd(o),e;if(i===r)return gd(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function qp(e){return e=c0(e),e!==null?Zp(e):null}function Zp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Zp(e);if(t!==null)return t;e=e.sibling}return null}var Jp=Je.unstable_scheduleCallback,yd=Je.unstable_cancelCallback,d0=Je.unstable_shouldYield,f0=Je.unstable_requestPaint,ve=Je.unstable_now,p0=Je.unstable_getCurrentPriorityLevel,Vu=Je.unstable_ImmediatePriority,eh=Je.unstable_UserBlockingPriority,ms=Je.unstable_NormalPriority,h0=Je.unstable_LowPriority,th=Je.unstable_IdlePriority,Qs=null,At=null;function m0(e){if(At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(Qs,e,void 0,(e.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:y0,v0=Math.log,g0=Math.LN2;function y0(e){return e>>>=0,e===0?32:31-(v0(e)/g0|0)|0}var ki=64,Pi=4194304;function ko(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function vs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=ko(a):(i&=s,i!==0&&(r=ko(i)))}else s=n&~o,s!==0?r=ko(s):i!==0&&(r=ko(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-gt(t),o=1<<n,r|=e[n],t&=~o;return r}function w0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function x0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-gt(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=w0(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Ml(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function nh(){var e=ki;return ki<<=1,!(ki&4194240)&&(ki=64),e}function Da(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ci(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-gt(t),e[t]=n}function S0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-gt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Wu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-gt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ee=0;function rh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var oh,Hu,ih,sh,ah,Il=!1,Ni=[],En=null,Cn=null,bn=null,Vo=new Map,Wo=new Map,fn=[],E0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function wd(e,t){switch(e){case"focusin":case"focusout":En=null;break;case"dragenter":case"dragleave":Cn=null;break;case"mouseover":case"mouseout":bn=null;break;case"pointerover":case"pointerout":Vo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wo.delete(t.pointerId)}}function mo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=fi(t),t!==null&&Hu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function C0(e,t,n,r,o){switch(t){case"focusin":return En=mo(En,e,t,n,r,o),!0;case"dragenter":return Cn=mo(Cn,e,t,n,r,o),!0;case"mouseover":return bn=mo(bn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Vo.set(i,mo(Vo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Wo.set(i,mo(Wo.get(i)||null,e,t,n,r,o)),!0}return!1}function lh(e){var t=Vn(e.target);if(t!==null){var n=sr(t);if(n!==null){if(t=n.tag,t===13){if(t=Xp(n),t!==null){e.blockedOn=t,ah(e.priority,function(){ih(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Dl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);_l=r,n.target.dispatchEvent(r),_l=null}else return t=fi(n),t!==null&&Hu(t),e.blockedOn=n,!1;t.shift()}return!0}function xd(e,t,n){Yi(e)&&n.delete(t)}function b0(){Il=!1,En!==null&&Yi(En)&&(En=null),Cn!==null&&Yi(Cn)&&(Cn=null),bn!==null&&Yi(bn)&&(bn=null),Vo.forEach(xd),Wo.forEach(xd)}function vo(e,t){e.blockedOn===t&&(e.blockedOn=null,Il||(Il=!0,Je.unstable_scheduleCallback(Je.unstable_NormalPriority,b0)))}function Ho(e){function t(o){return vo(o,e)}if(0<Ni.length){vo(Ni[0],e);for(var n=1;n<Ni.length;n++){var r=Ni[n];r.blockedOn===e&&(r.blockedOn=null)}}for(En!==null&&vo(En,e),Cn!==null&&vo(Cn,e),bn!==null&&vo(bn,e),Vo.forEach(t),Wo.forEach(t),n=0;n<fn.length;n++)r=fn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<fn.length&&(n=fn[0],n.blockedOn===null);)lh(n),n.blockedOn===null&&fn.shift()}var jr=Xt.ReactCurrentBatchConfig,gs=!0;function k0(e,t,n,r){var o=ee,i=jr.transition;jr.transition=null;try{ee=1,Qu(e,t,n,r)}finally{ee=o,jr.transition=i}}function P0(e,t,n,r){var o=ee,i=jr.transition;jr.transition=null;try{ee=4,Qu(e,t,n,r)}finally{ee=o,jr.transition=i}}function Qu(e,t,n,r){if(gs){var o=Dl(e,t,n,r);if(o===null)Qa(e,t,r,ys,n),wd(e,r);else if(C0(o,e,t,n,r))r.stopPropagation();else if(wd(e,r),t&4&&-1<E0.indexOf(e)){for(;o!==null;){var i=fi(o);if(i!==null&&oh(i),i=Dl(e,t,n,r),i===null&&Qa(e,t,r,ys,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Qa(e,t,r,null,n)}}var ys=null;function Dl(e,t,n,r){if(ys=null,e=Bu(r),e=Vn(e),e!==null)if(t=sr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Xp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ys=e,null}function uh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(p0()){case Vu:return 1;case eh:return 4;case ms:case h0:return 16;case th:return 536870912;default:return 16}default:return 16}}var wn=null,Ku=null,Xi=null;function ch(){if(Xi)return Xi;var e,t=Ku,n=t.length,r,o="value"in wn?wn.value:wn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Xi=o.slice(e,1<r?1-r:void 0)}function qi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ti(){return!0}function Sd(){return!1}function nt(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ti:Sd,this.isPropagationStopped=Sd,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ti)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ti)},persist:function(){},isPersistent:Ti}),t}var so={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Gu=nt(so),di=pe({},so,{view:0,detail:0}),N0=nt(di),La,Fa,go,Ks=pe({},di,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Yu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==go&&(go&&e.type==="mousemove"?(La=e.screenX-go.screenX,Fa=e.screenY-go.screenY):Fa=La=0,go=e),La)},movementY:function(e){return"movementY"in e?e.movementY:Fa}}),Ed=nt(Ks),T0=pe({},Ks,{dataTransfer:0}),R0=nt(T0),_0=pe({},di,{relatedTarget:0}),za=nt(_0),O0=pe({},so,{animationName:0,elapsedTime:0,pseudoElement:0}),A0=nt(O0),j0=pe({},so,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),M0=nt(j0),I0=pe({},so,{data:0}),Cd=nt(I0),D0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},L0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},F0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function z0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=F0[e])?!!t[e]:!1}function Yu(){return z0}var $0=pe({},di,{key:function(e){if(e.key){var t=D0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?L0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Yu,charCode:function(e){return e.type==="keypress"?qi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),U0=nt($0),B0=pe({},Ks,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bd=nt(B0),V0=pe({},di,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Yu}),W0=nt(V0),H0=pe({},so,{propertyName:0,elapsedTime:0,pseudoElement:0}),Q0=nt(H0),K0=pe({},Ks,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),G0=nt(K0),Y0=[9,13,27,32],Xu=Wt&&"CompositionEvent"in window,Ro=null;Wt&&"documentMode"in document&&(Ro=document.documentMode);var X0=Wt&&"TextEvent"in window&&!Ro,dh=Wt&&(!Xu||Ro&&8<Ro&&11>=Ro),kd=" ",Pd=!1;function fh(e,t){switch(e){case"keyup":return Y0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ph(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yr=!1;function q0(e,t){switch(e){case"compositionend":return ph(t);case"keypress":return t.which!==32?null:(Pd=!0,kd);case"textInput":return e=t.data,e===kd&&Pd?null:e;default:return null}}function Z0(e,t){if(yr)return e==="compositionend"||!Xu&&fh(e,t)?(e=ch(),Xi=Ku=wn=null,yr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return dh&&t.locale!=="ko"?null:t.data;default:return null}}var J0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Nd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!J0[e.type]:t==="textarea"}function hh(e,t,n,r){Hp(r),t=ws(t,"onChange"),0<t.length&&(n=new Gu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var _o=null,Qo=null;function ew(e){kh(e,0)}function Gs(e){var t=Sr(e);if(Fp(t))return e}function tw(e,t){if(e==="change")return t}var mh=!1;if(Wt){var $a;if(Wt){var Ua="oninput"in document;if(!Ua){var Td=document.createElement("div");Td.setAttribute("oninput","return;"),Ua=typeof Td.oninput=="function"}$a=Ua}else $a=!1;mh=$a&&(!document.documentMode||9<document.documentMode)}function Rd(){_o&&(_o.detachEvent("onpropertychange",vh),Qo=_o=null)}function vh(e){if(e.propertyName==="value"&&Gs(Qo)){var t=[];hh(t,Qo,e,Bu(e)),Yp(ew,t)}}function nw(e,t,n){e==="focusin"?(Rd(),_o=t,Qo=n,_o.attachEvent("onpropertychange",vh)):e==="focusout"&&Rd()}function rw(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Gs(Qo)}function ow(e,t){if(e==="click")return Gs(t)}function iw(e,t){if(e==="input"||e==="change")return Gs(t)}function sw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wt=typeof Object.is=="function"?Object.is:sw;function Ko(e,t){if(wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!yl.call(t,o)||!wt(e[o],t[o]))return!1}return!0}function _d(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Od(e,t){var n=_d(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=_d(n)}}function gh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?gh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yh(){for(var e=window,t=fs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=fs(e.document)}return t}function qu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function aw(e){var t=yh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&gh(n.ownerDocument.documentElement,n)){if(r!==null&&qu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Od(n,i);var s=Od(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var lw=Wt&&"documentMode"in document&&11>=document.documentMode,wr=null,Ll=null,Oo=null,Fl=!1;function Ad(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Fl||wr==null||wr!==fs(r)||(r=wr,"selectionStart"in r&&qu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Oo&&Ko(Oo,r)||(Oo=r,r=ws(Ll,"onSelect"),0<r.length&&(t=new Gu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=wr)))}function Ri(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var xr={animationend:Ri("Animation","AnimationEnd"),animationiteration:Ri("Animation","AnimationIteration"),animationstart:Ri("Animation","AnimationStart"),transitionend:Ri("Transition","TransitionEnd")},Ba={},wh={};Wt&&(wh=document.createElement("div").style,"AnimationEvent"in window||(delete xr.animationend.animation,delete xr.animationiteration.animation,delete xr.animationstart.animation),"TransitionEvent"in window||delete xr.transitionend.transition);function Ys(e){if(Ba[e])return Ba[e];if(!xr[e])return e;var t=xr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wh)return Ba[e]=t[n];return e}var xh=Ys("animationend"),Sh=Ys("animationiteration"),Eh=Ys("animationstart"),Ch=Ys("transitionend"),bh=new Map,jd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ln(e,t){bh.set(e,t),ir(t,[e])}for(var Va=0;Va<jd.length;Va++){var Wa=jd[Va],uw=Wa.toLowerCase(),cw=Wa[0].toUpperCase()+Wa.slice(1);Ln(uw,"on"+cw)}Ln(xh,"onAnimationEnd");Ln(Sh,"onAnimationIteration");Ln(Eh,"onAnimationStart");Ln("dblclick","onDoubleClick");Ln("focusin","onFocus");Ln("focusout","onBlur");Ln(Ch,"onTransitionEnd");Yr("onMouseEnter",["mouseout","mouseover"]);Yr("onMouseLeave",["mouseout","mouseover"]);Yr("onPointerEnter",["pointerout","pointerover"]);Yr("onPointerLeave",["pointerout","pointerover"]);ir("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ir("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ir("onBeforeInput",["compositionend","keypress","textInput","paste"]);ir("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Po="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),dw=new Set("cancel close invalid load scroll toggle".split(" ").concat(Po));function Md(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,u0(r,t,void 0,e),e.currentTarget=null}function kh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;Md(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;Md(o,a,u),i=l}}}if(hs)throw e=jl,hs=!1,jl=null,e}function ae(e,t){var n=t[Vl];n===void 0&&(n=t[Vl]=new Set);var r=e+"__bubble";n.has(r)||(Ph(t,e,2,!1),n.add(r))}function Ha(e,t,n){var r=0;t&&(r|=4),Ph(n,e,r,t)}var _i="_reactListening"+Math.random().toString(36).slice(2);function Go(e){if(!e[_i]){e[_i]=!0,jp.forEach(function(n){n!=="selectionchange"&&(dw.has(n)||Ha(n,!1,e),Ha(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[_i]||(t[_i]=!0,Ha("selectionchange",!1,t))}}function Ph(e,t,n,r){switch(uh(t)){case 1:var o=k0;break;case 4:o=P0;break;default:o=Qu}n=o.bind(null,t,n,e),o=void 0,!Al||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Qa(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Vn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Yp(function(){var u=i,f=Bu(n),d=[];e:{var c=bh.get(e);if(c!==void 0){var y=Gu,x=e;switch(e){case"keypress":if(qi(n)===0)break e;case"keydown":case"keyup":y=U0;break;case"focusin":x="focus",y=za;break;case"focusout":x="blur",y=za;break;case"beforeblur":case"afterblur":y=za;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Ed;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=R0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=W0;break;case xh:case Sh:case Eh:y=A0;break;case Ch:y=Q0;break;case"scroll":y=N0;break;case"wheel":y=G0;break;case"copy":case"cut":case"paste":y=M0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=bd}var p=(t&4)!==0,S=!p&&e==="scroll",m=p?c!==null?c+"Capture":null:c;p=[];for(var v=u,w;v!==null;){w=v;var E=w.stateNode;if(w.tag===5&&E!==null&&(w=E,m!==null&&(E=Bo(v,m),E!=null&&p.push(Yo(v,E,w)))),S)break;v=v.return}0<p.length&&(c=new y(c,x,null,n,f),d.push({event:c,listeners:p}))}}if(!(t&7)){e:{if(c=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",c&&n!==_l&&(x=n.relatedTarget||n.fromElement)&&(Vn(x)||x[Ht]))break e;if((y||c)&&(c=f.window===f?f:(c=f.ownerDocument)?c.defaultView||c.parentWindow:window,y?(x=n.relatedTarget||n.toElement,y=u,x=x?Vn(x):null,x!==null&&(S=sr(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(y=null,x=u),y!==x)){if(p=Ed,E="onMouseLeave",m="onMouseEnter",v="mouse",(e==="pointerout"||e==="pointerover")&&(p=bd,E="onPointerLeave",m="onPointerEnter",v="pointer"),S=y==null?c:Sr(y),w=x==null?c:Sr(x),c=new p(E,v+"leave",y,n,f),c.target=S,c.relatedTarget=w,E=null,Vn(f)===u&&(p=new p(m,v+"enter",x,n,f),p.target=w,p.relatedTarget=S,E=p),S=E,y&&x)t:{for(p=y,m=x,v=0,w=p;w;w=fr(w))v++;for(w=0,E=m;E;E=fr(E))w++;for(;0<v-w;)p=fr(p),v--;for(;0<w-v;)m=fr(m),w--;for(;v--;){if(p===m||m!==null&&p===m.alternate)break t;p=fr(p),m=fr(m)}p=null}else p=null;y!==null&&Id(d,c,y,p,!1),x!==null&&S!==null&&Id(d,S,x,p,!0)}}e:{if(c=u?Sr(u):window,y=c.nodeName&&c.nodeName.toLowerCase(),y==="select"||y==="input"&&c.type==="file")var C=tw;else if(Nd(c))if(mh)C=iw;else{C=rw;var k=nw}else(y=c.nodeName)&&y.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(C=ow);if(C&&(C=C(e,u))){hh(d,C,n,f);break e}k&&k(e,c,u),e==="focusout"&&(k=c._wrapperState)&&k.controlled&&c.type==="number"&&kl(c,"number",c.value)}switch(k=u?Sr(u):window,e){case"focusin":(Nd(k)||k.contentEditable==="true")&&(wr=k,Ll=u,Oo=null);break;case"focusout":Oo=Ll=wr=null;break;case"mousedown":Fl=!0;break;case"contextmenu":case"mouseup":case"dragend":Fl=!1,Ad(d,n,f);break;case"selectionchange":if(lw)break;case"keydown":case"keyup":Ad(d,n,f)}var N;if(Xu)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else yr?fh(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(dh&&n.locale!=="ko"&&(yr||P!=="onCompositionStart"?P==="onCompositionEnd"&&yr&&(N=ch()):(wn=f,Ku="value"in wn?wn.value:wn.textContent,yr=!0)),k=ws(u,P),0<k.length&&(P=new Cd(P,e,null,n,f),d.push({event:P,listeners:k}),N?P.data=N:(N=ph(n),N!==null&&(P.data=N)))),(N=X0?q0(e,n):Z0(e,n))&&(u=ws(u,"onBeforeInput"),0<u.length&&(f=new Cd("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=N))}kh(d,t)})}function Yo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ws(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Bo(e,n),i!=null&&r.unshift(Yo(e,i,o)),i=Bo(e,t),i!=null&&r.push(Yo(e,i,o))),e=e.return}return r}function fr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Id(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=Bo(n,i),l!=null&&s.unshift(Yo(n,l,a))):o||(l=Bo(n,i),l!=null&&s.push(Yo(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var fw=/\r\n?/g,pw=/\u0000|\uFFFD/g;function Dd(e){return(typeof e=="string"?e:""+e).replace(fw,`
`).replace(pw,"")}function Oi(e,t,n){if(t=Dd(t),Dd(e)!==t&&n)throw Error(R(425))}function xs(){}var zl=null,$l=null;function Ul(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Bl=typeof setTimeout=="function"?setTimeout:void 0,hw=typeof clearTimeout=="function"?clearTimeout:void 0,Ld=typeof Promise=="function"?Promise:void 0,mw=typeof queueMicrotask=="function"?queueMicrotask:typeof Ld<"u"?function(e){return Ld.resolve(null).then(e).catch(vw)}:Bl;function vw(e){setTimeout(function(){throw e})}function Ka(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Ho(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Ho(t)}function kn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Fd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ao=Math.random().toString(36).slice(2),_t="__reactFiber$"+ao,Xo="__reactProps$"+ao,Ht="__reactContainer$"+ao,Vl="__reactEvents$"+ao,gw="__reactListeners$"+ao,yw="__reactHandles$"+ao;function Vn(e){var t=e[_t];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ht]||n[_t]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Fd(e);e!==null;){if(n=e[_t])return n;e=Fd(e)}return t}e=n,n=e.parentNode}return null}function fi(e){return e=e[_t]||e[Ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Sr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function Xs(e){return e[Xo]||null}var Wl=[],Er=-1;function Fn(e){return{current:e}}function le(e){0>Er||(e.current=Wl[Er],Wl[Er]=null,Er--)}function oe(e,t){Er++,Wl[Er]=e.current,e.current=t}var On={},Oe=Fn(On),Ue=Fn(!1),Zn=On;function Xr(e,t){var n=e.type.contextTypes;if(!n)return On;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Be(e){return e=e.childContextTypes,e!=null}function Ss(){le(Ue),le(Oe)}function zd(e,t,n){if(Oe.current!==On)throw Error(R(168));oe(Oe,t),oe(Ue,n)}function Nh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,n0(e)||"Unknown",o));return pe({},n,r)}function Es(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||On,Zn=Oe.current,oe(Oe,e),oe(Ue,Ue.current),!0}function $d(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=Nh(e,t,Zn),r.__reactInternalMemoizedMergedChildContext=e,le(Ue),le(Oe),oe(Oe,e)):le(Ue),oe(Ue,n)}var zt=null,qs=!1,Ga=!1;function Th(e){zt===null?zt=[e]:zt.push(e)}function ww(e){qs=!0,Th(e)}function zn(){if(!Ga&&zt!==null){Ga=!0;var e=0,t=ee;try{var n=zt;for(ee=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}zt=null,qs=!1}catch(o){throw zt!==null&&(zt=zt.slice(e+1)),Jp(Vu,zn),o}finally{ee=t,Ga=!1}}return null}var Cr=[],br=0,Cs=null,bs=0,ot=[],it=0,Jn=null,Ut=1,Bt="";function Un(e,t){Cr[br++]=bs,Cr[br++]=Cs,Cs=e,bs=t}function Rh(e,t,n){ot[it++]=Ut,ot[it++]=Bt,ot[it++]=Jn,Jn=e;var r=Ut;e=Bt;var o=32-gt(r)-1;r&=~(1<<o),n+=1;var i=32-gt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Ut=1<<32-gt(t)+o|n<<o|r,Bt=i+e}else Ut=1<<i|n<<o|r,Bt=e}function Zu(e){e.return!==null&&(Un(e,1),Rh(e,1,0))}function Ju(e){for(;e===Cs;)Cs=Cr[--br],Cr[br]=null,bs=Cr[--br],Cr[br]=null;for(;e===Jn;)Jn=ot[--it],ot[it]=null,Bt=ot[--it],ot[it]=null,Ut=ot[--it],ot[it]=null}var qe=null,Xe=null,ue=!1,vt=null;function _h(e,t){var n=st(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ud(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,qe=e,Xe=kn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,qe=e,Xe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Jn!==null?{id:Ut,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=st(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,qe=e,Xe=null,!0):!1;default:return!1}}function Hl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ql(e){if(ue){var t=Xe;if(t){var n=t;if(!Ud(e,t)){if(Hl(e))throw Error(R(418));t=kn(n.nextSibling);var r=qe;t&&Ud(e,t)?_h(r,n):(e.flags=e.flags&-4097|2,ue=!1,qe=e)}}else{if(Hl(e))throw Error(R(418));e.flags=e.flags&-4097|2,ue=!1,qe=e}}}function Bd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;qe=e}function Ai(e){if(e!==qe)return!1;if(!ue)return Bd(e),ue=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ul(e.type,e.memoizedProps)),t&&(t=Xe)){if(Hl(e))throw Oh(),Error(R(418));for(;t;)_h(e,t),t=kn(t.nextSibling)}if(Bd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Xe=kn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Xe=null}}else Xe=qe?kn(e.stateNode.nextSibling):null;return!0}function Oh(){for(var e=Xe;e;)e=kn(e.nextSibling)}function qr(){Xe=qe=null,ue=!1}function ec(e){vt===null?vt=[e]:vt.push(e)}var xw=Xt.ReactCurrentBatchConfig;function yo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function ji(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Vd(e){var t=e._init;return t(e._payload)}function Ah(e){function t(m,v){if(e){var w=m.deletions;w===null?(m.deletions=[v],m.flags|=16):w.push(v)}}function n(m,v){if(!e)return null;for(;v!==null;)t(m,v),v=v.sibling;return null}function r(m,v){for(m=new Map;v!==null;)v.key!==null?m.set(v.key,v):m.set(v.index,v),v=v.sibling;return m}function o(m,v){return m=Rn(m,v),m.index=0,m.sibling=null,m}function i(m,v,w){return m.index=w,e?(w=m.alternate,w!==null?(w=w.index,w<v?(m.flags|=2,v):w):(m.flags|=2,v)):(m.flags|=1048576,v)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,v,w,E){return v===null||v.tag!==6?(v=tl(w,m.mode,E),v.return=m,v):(v=o(v,w),v.return=m,v)}function l(m,v,w,E){var C=w.type;return C===gr?f(m,v,w.props.children,E,w.key):v!==null&&(v.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===un&&Vd(C)===v.type)?(E=o(v,w.props),E.ref=yo(m,v,w),E.return=m,E):(E=os(w.type,w.key,w.props,null,m.mode,E),E.ref=yo(m,v,w),E.return=m,E)}function u(m,v,w,E){return v===null||v.tag!==4||v.stateNode.containerInfo!==w.containerInfo||v.stateNode.implementation!==w.implementation?(v=nl(w,m.mode,E),v.return=m,v):(v=o(v,w.children||[]),v.return=m,v)}function f(m,v,w,E,C){return v===null||v.tag!==7?(v=qn(w,m.mode,E,C),v.return=m,v):(v=o(v,w),v.return=m,v)}function d(m,v,w){if(typeof v=="string"&&v!==""||typeof v=="number")return v=tl(""+v,m.mode,w),v.return=m,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Ei:return w=os(v.type,v.key,v.props,null,m.mode,w),w.ref=yo(m,null,v),w.return=m,w;case vr:return v=nl(v,m.mode,w),v.return=m,v;case un:var E=v._init;return d(m,E(v._payload),w)}if(bo(v)||po(v))return v=qn(v,m.mode,w,null),v.return=m,v;ji(m,v)}return null}function c(m,v,w,E){var C=v!==null?v.key:null;if(typeof w=="string"&&w!==""||typeof w=="number")return C!==null?null:a(m,v,""+w,E);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Ei:return w.key===C?l(m,v,w,E):null;case vr:return w.key===C?u(m,v,w,E):null;case un:return C=w._init,c(m,v,C(w._payload),E)}if(bo(w)||po(w))return C!==null?null:f(m,v,w,E,null);ji(m,w)}return null}function y(m,v,w,E,C){if(typeof E=="string"&&E!==""||typeof E=="number")return m=m.get(w)||null,a(v,m,""+E,C);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Ei:return m=m.get(E.key===null?w:E.key)||null,l(v,m,E,C);case vr:return m=m.get(E.key===null?w:E.key)||null,u(v,m,E,C);case un:var k=E._init;return y(m,v,w,k(E._payload),C)}if(bo(E)||po(E))return m=m.get(w)||null,f(v,m,E,C,null);ji(v,E)}return null}function x(m,v,w,E){for(var C=null,k=null,N=v,P=v=0,M=null;N!==null&&P<w.length;P++){N.index>P?(M=N,N=null):M=N.sibling;var A=c(m,N,w[P],E);if(A===null){N===null&&(N=M);break}e&&N&&A.alternate===null&&t(m,N),v=i(A,v,P),k===null?C=A:k.sibling=A,k=A,N=M}if(P===w.length)return n(m,N),ue&&Un(m,P),C;if(N===null){for(;P<w.length;P++)N=d(m,w[P],E),N!==null&&(v=i(N,v,P),k===null?C=N:k.sibling=N,k=N);return ue&&Un(m,P),C}for(N=r(m,N);P<w.length;P++)M=y(N,m,P,w[P],E),M!==null&&(e&&M.alternate!==null&&N.delete(M.key===null?P:M.key),v=i(M,v,P),k===null?C=M:k.sibling=M,k=M);return e&&N.forEach(function($){return t(m,$)}),ue&&Un(m,P),C}function p(m,v,w,E){var C=po(w);if(typeof C!="function")throw Error(R(150));if(w=C.call(w),w==null)throw Error(R(151));for(var k=C=null,N=v,P=v=0,M=null,A=w.next();N!==null&&!A.done;P++,A=w.next()){N.index>P?(M=N,N=null):M=N.sibling;var $=c(m,N,A.value,E);if($===null){N===null&&(N=M);break}e&&N&&$.alternate===null&&t(m,N),v=i($,v,P),k===null?C=$:k.sibling=$,k=$,N=M}if(A.done)return n(m,N),ue&&Un(m,P),C;if(N===null){for(;!A.done;P++,A=w.next())A=d(m,A.value,E),A!==null&&(v=i(A,v,P),k===null?C=A:k.sibling=A,k=A);return ue&&Un(m,P),C}for(N=r(m,N);!A.done;P++,A=w.next())A=y(N,m,P,A.value,E),A!==null&&(e&&A.alternate!==null&&N.delete(A.key===null?P:A.key),v=i(A,v,P),k===null?C=A:k.sibling=A,k=A);return e&&N.forEach(function(L){return t(m,L)}),ue&&Un(m,P),C}function S(m,v,w,E){if(typeof w=="object"&&w!==null&&w.type===gr&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case Ei:e:{for(var C=w.key,k=v;k!==null;){if(k.key===C){if(C=w.type,C===gr){if(k.tag===7){n(m,k.sibling),v=o(k,w.props.children),v.return=m,m=v;break e}}else if(k.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===un&&Vd(C)===k.type){n(m,k.sibling),v=o(k,w.props),v.ref=yo(m,k,w),v.return=m,m=v;break e}n(m,k);break}else t(m,k);k=k.sibling}w.type===gr?(v=qn(w.props.children,m.mode,E,w.key),v.return=m,m=v):(E=os(w.type,w.key,w.props,null,m.mode,E),E.ref=yo(m,v,w),E.return=m,m=E)}return s(m);case vr:e:{for(k=w.key;v!==null;){if(v.key===k)if(v.tag===4&&v.stateNode.containerInfo===w.containerInfo&&v.stateNode.implementation===w.implementation){n(m,v.sibling),v=o(v,w.children||[]),v.return=m,m=v;break e}else{n(m,v);break}else t(m,v);v=v.sibling}v=nl(w,m.mode,E),v.return=m,m=v}return s(m);case un:return k=w._init,S(m,v,k(w._payload),E)}if(bo(w))return x(m,v,w,E);if(po(w))return p(m,v,w,E);ji(m,w)}return typeof w=="string"&&w!==""||typeof w=="number"?(w=""+w,v!==null&&v.tag===6?(n(m,v.sibling),v=o(v,w),v.return=m,m=v):(n(m,v),v=tl(w,m.mode,E),v.return=m,m=v),s(m)):n(m,v)}return S}var Zr=Ah(!0),jh=Ah(!1),ks=Fn(null),Ps=null,kr=null,tc=null;function nc(){tc=kr=Ps=null}function rc(e){var t=ks.current;le(ks),e._currentValue=t}function Kl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Mr(e,t){Ps=e,tc=kr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&($e=!0),e.firstContext=null)}function lt(e){var t=e._currentValue;if(tc!==e)if(e={context:e,memoizedValue:t,next:null},kr===null){if(Ps===null)throw Error(R(308));kr=e,Ps.dependencies={lanes:0,firstContext:e}}else kr=kr.next=e;return t}var Wn=null;function oc(e){Wn===null?Wn=[e]:Wn.push(e)}function Mh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,oc(t)):(n.next=o.next,o.next=n),t.interleaved=n,Qt(e,r)}function Qt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var cn=!1;function ic(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ih(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Pn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,q&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Qt(e,n)}return o=r.interleaved,o===null?(t.next=t,oc(r)):(t.next=o.next,o.next=t),r.interleaved=t,Qt(e,n)}function Zi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wu(e,n)}}function Wd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ns(e,t,n,r){var o=e.updateQueue;cn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==s&&(a===null?f.firstBaseUpdate=u:a.next=u,f.lastBaseUpdate=l))}if(i!==null){var d=o.baseState;s=0,f=u=l=null,a=i;do{var c=a.lane,y=a.eventTime;if((r&c)===c){f!==null&&(f=f.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,p=a;switch(c=t,y=n,p.tag){case 1:if(x=p.payload,typeof x=="function"){d=x.call(y,d,c);break e}d=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=p.payload,c=typeof x=="function"?x.call(y,d,c):x,c==null)break e;d=pe({},d,c);break e;case 2:cn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,c=o.effects,c===null?o.effects=[a]:c.push(a))}else y={eventTime:y,lane:c,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(u=f=y,l=d):f=f.next=y,s|=c;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;c=a,a=c.next,c.next=null,o.lastBaseUpdate=c,o.shared.pending=null}}while(!0);if(f===null&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);tr|=s,e.lanes=s,e.memoizedState=d}}function Hd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var pi={},jt=Fn(pi),qo=Fn(pi),Zo=Fn(pi);function Hn(e){if(e===pi)throw Error(R(174));return e}function sc(e,t){switch(oe(Zo,t),oe(qo,e),oe(jt,pi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Nl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Nl(t,e)}le(jt),oe(jt,t)}function Jr(){le(jt),le(qo),le(Zo)}function Dh(e){Hn(Zo.current);var t=Hn(jt.current),n=Nl(t,e.type);t!==n&&(oe(qo,e),oe(jt,n))}function ac(e){qo.current===e&&(le(jt),le(qo))}var de=Fn(0);function Ts(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ya=[];function lc(){for(var e=0;e<Ya.length;e++)Ya[e]._workInProgressVersionPrimary=null;Ya.length=0}var Ji=Xt.ReactCurrentDispatcher,Xa=Xt.ReactCurrentBatchConfig,er=0,fe=null,ye=null,xe=null,Rs=!1,Ao=!1,Jo=0,Sw=0;function Ne(){throw Error(R(321))}function uc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!wt(e[n],t[n]))return!1;return!0}function cc(e,t,n,r,o,i){if(er=i,fe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ji.current=e===null||e.memoizedState===null?kw:Pw,e=n(r,o),Ao){i=0;do{if(Ao=!1,Jo=0,25<=i)throw Error(R(301));i+=1,xe=ye=null,t.updateQueue=null,Ji.current=Nw,e=n(r,o)}while(Ao)}if(Ji.current=_s,t=ye!==null&&ye.next!==null,er=0,xe=ye=fe=null,Rs=!1,t)throw Error(R(300));return e}function dc(){var e=Jo!==0;return Jo=0,e}function Pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?fe.memoizedState=xe=e:xe=xe.next=e,xe}function ut(){if(ye===null){var e=fe.alternate;e=e!==null?e.memoizedState:null}else e=ye.next;var t=xe===null?fe.memoizedState:xe.next;if(t!==null)xe=t,ye=e;else{if(e===null)throw Error(R(310));ye=e,e={memoizedState:ye.memoizedState,baseState:ye.baseState,baseQueue:ye.baseQueue,queue:ye.queue,next:null},xe===null?fe.memoizedState=xe=e:xe=xe.next=e}return xe}function ei(e,t){return typeof t=="function"?t(e):t}function qa(e){var t=ut(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=ye,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var f=u.lane;if((er&f)===f)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,s=r):l=l.next=d,fe.lanes|=f,tr|=f}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,wt(r,t.memoizedState)||($e=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,fe.lanes|=i,tr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Za(e){var t=ut(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);wt(i,t.memoizedState)||($e=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Lh(){}function Fh(e,t){var n=fe,r=ut(),o=t(),i=!wt(r.memoizedState,o);if(i&&(r.memoizedState=o,$e=!0),r=r.queue,fc(Uh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||xe!==null&&xe.memoizedState.tag&1){if(n.flags|=2048,ti(9,$h.bind(null,n,r,o,t),void 0,null),Se===null)throw Error(R(349));er&30||zh(n,t,o)}return o}function zh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=fe.updateQueue,t===null?(t={lastEffect:null,stores:null},fe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function $h(e,t,n,r){t.value=n,t.getSnapshot=r,Bh(t)&&Vh(e)}function Uh(e,t,n){return n(function(){Bh(t)&&Vh(e)})}function Bh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!wt(e,n)}catch{return!0}}function Vh(e){var t=Qt(e,1);t!==null&&yt(t,e,1,-1)}function Qd(e){var t=Pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ei,lastRenderedState:e},t.queue=e,e=e.dispatch=bw.bind(null,fe,e),[t.memoizedState,e]}function ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=fe.updateQueue,t===null?(t={lastEffect:null,stores:null},fe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wh(){return ut().memoizedState}function es(e,t,n,r){var o=Pt();fe.flags|=e,o.memoizedState=ti(1|t,n,void 0,r===void 0?null:r)}function Zs(e,t,n,r){var o=ut();r=r===void 0?null:r;var i=void 0;if(ye!==null){var s=ye.memoizedState;if(i=s.destroy,r!==null&&uc(r,s.deps)){o.memoizedState=ti(t,n,i,r);return}}fe.flags|=e,o.memoizedState=ti(1|t,n,i,r)}function Kd(e,t){return es(8390656,8,e,t)}function fc(e,t){return Zs(2048,8,e,t)}function Hh(e,t){return Zs(4,2,e,t)}function Qh(e,t){return Zs(4,4,e,t)}function Kh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Gh(e,t,n){return n=n!=null?n.concat([e]):null,Zs(4,4,Kh.bind(null,t,e),n)}function pc(){}function Yh(e,t){var n=ut();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&uc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xh(e,t){var n=ut();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&uc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function qh(e,t,n){return er&21?(wt(n,t)||(n=nh(),fe.lanes|=n,tr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,$e=!0),e.memoizedState=n)}function Ew(e,t){var n=ee;ee=n!==0&&4>n?n:4,e(!0);var r=Xa.transition;Xa.transition={};try{e(!1),t()}finally{ee=n,Xa.transition=r}}function Zh(){return ut().memoizedState}function Cw(e,t,n){var r=Tn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jh(e))em(t,n);else if(n=Mh(e,t,n,r),n!==null){var o=De();yt(n,e,r,o),tm(n,t,r)}}function bw(e,t,n){var r=Tn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jh(e))em(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,wt(a,s)){var l=t.interleaved;l===null?(o.next=o,oc(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=Mh(e,t,o,r),n!==null&&(o=De(),yt(n,e,r,o),tm(n,t,r))}}function Jh(e){var t=e.alternate;return e===fe||t!==null&&t===fe}function em(e,t){Ao=Rs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function tm(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wu(e,n)}}var _s={readContext:lt,useCallback:Ne,useContext:Ne,useEffect:Ne,useImperativeHandle:Ne,useInsertionEffect:Ne,useLayoutEffect:Ne,useMemo:Ne,useReducer:Ne,useRef:Ne,useState:Ne,useDebugValue:Ne,useDeferredValue:Ne,useTransition:Ne,useMutableSource:Ne,useSyncExternalStore:Ne,useId:Ne,unstable_isNewReconciler:!1},kw={readContext:lt,useCallback:function(e,t){return Pt().memoizedState=[e,t===void 0?null:t],e},useContext:lt,useEffect:Kd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,es(4194308,4,Kh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return es(4194308,4,e,t)},useInsertionEffect:function(e,t){return es(4,2,e,t)},useMemo:function(e,t){var n=Pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Cw.bind(null,fe,e),[r.memoizedState,e]},useRef:function(e){var t=Pt();return e={current:e},t.memoizedState=e},useState:Qd,useDebugValue:pc,useDeferredValue:function(e){return Pt().memoizedState=e},useTransition:function(){var e=Qd(!1),t=e[0];return e=Ew.bind(null,e[1]),Pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=fe,o=Pt();if(ue){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),Se===null)throw Error(R(349));er&30||zh(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Kd(Uh.bind(null,r,i,e),[e]),r.flags|=2048,ti(9,$h.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Pt(),t=Se.identifierPrefix;if(ue){var n=Bt,r=Ut;n=(r&~(1<<32-gt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Jo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Sw++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Pw={readContext:lt,useCallback:Yh,useContext:lt,useEffect:fc,useImperativeHandle:Gh,useInsertionEffect:Hh,useLayoutEffect:Qh,useMemo:Xh,useReducer:qa,useRef:Wh,useState:function(){return qa(ei)},useDebugValue:pc,useDeferredValue:function(e){var t=ut();return qh(t,ye.memoizedState,e)},useTransition:function(){var e=qa(ei)[0],t=ut().memoizedState;return[e,t]},useMutableSource:Lh,useSyncExternalStore:Fh,useId:Zh,unstable_isNewReconciler:!1},Nw={readContext:lt,useCallback:Yh,useContext:lt,useEffect:fc,useImperativeHandle:Gh,useInsertionEffect:Hh,useLayoutEffect:Qh,useMemo:Xh,useReducer:Za,useRef:Wh,useState:function(){return Za(ei)},useDebugValue:pc,useDeferredValue:function(e){var t=ut();return ye===null?t.memoizedState=e:qh(t,ye.memoizedState,e)},useTransition:function(){var e=Za(ei)[0],t=ut().memoizedState;return[e,t]},useMutableSource:Lh,useSyncExternalStore:Fh,useId:Zh,unstable_isNewReconciler:!1};function ft(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Gl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:pe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Js={isMounted:function(e){return(e=e._reactInternals)?sr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=De(),o=Tn(e),i=Vt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Pn(e,i,o),t!==null&&(yt(t,e,o,r),Zi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=De(),o=Tn(e),i=Vt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Pn(e,i,o),t!==null&&(yt(t,e,o,r),Zi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=De(),r=Tn(e),o=Vt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Pn(e,o,r),t!==null&&(yt(t,e,r,n),Zi(t,e,r))}};function Gd(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Ko(n,r)||!Ko(o,i):!0}function nm(e,t,n){var r=!1,o=On,i=t.contextType;return typeof i=="object"&&i!==null?i=lt(i):(o=Be(t)?Zn:Oe.current,r=t.contextTypes,i=(r=r!=null)?Xr(e,o):On),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Js,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Yd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Js.enqueueReplaceState(t,t.state,null)}function Yl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},ic(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=lt(i):(i=Be(t)?Zn:Oe.current,o.context=Xr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Gl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Js.enqueueReplaceState(o,o.state,null),Ns(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function eo(e,t){try{var n="",r=t;do n+=t0(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ja(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Xl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Tw=typeof WeakMap=="function"?WeakMap:Map;function rm(e,t,n){n=Vt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){As||(As=!0,su=r),Xl(e,t)},n}function om(e,t,n){n=Vt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Xl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Xl(e,t),typeof r!="function"&&(Nn===null?Nn=new Set([this]):Nn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Xd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Tw;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Bw.bind(null,e,t,n),t.then(e,e))}function qd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Zd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Vt(-1,1),t.tag=2,Pn(n,t,1))),n.lanes|=1),e)}var Rw=Xt.ReactCurrentOwner,$e=!1;function Me(e,t,n,r){t.child=e===null?jh(t,null,n,r):Zr(t,e.child,n,r)}function Jd(e,t,n,r,o){n=n.render;var i=t.ref;return Mr(t,o),r=cc(e,t,n,r,i,o),n=dc(),e!==null&&!$e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Kt(e,t,o)):(ue&&n&&Zu(t),t.flags|=1,Me(e,t,r,o),t.child)}function ef(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Sc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,im(e,t,i,r,o)):(e=os(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ko,n(s,r)&&e.ref===t.ref)return Kt(e,t,o)}return t.flags|=1,e=Rn(i,r),e.ref=t.ref,e.return=t,t.child=e}function im(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Ko(i,r)&&e.ref===t.ref)if($e=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&($e=!0);else return t.lanes=e.lanes,Kt(e,t,o)}return ql(e,t,n,r,o)}function sm(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},oe(Nr,Ge),Ge|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,oe(Nr,Ge),Ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,oe(Nr,Ge),Ge|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,oe(Nr,Ge),Ge|=r;return Me(e,t,o,n),t.child}function am(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ql(e,t,n,r,o){var i=Be(n)?Zn:Oe.current;return i=Xr(t,i),Mr(t,o),n=cc(e,t,n,r,i,o),r=dc(),e!==null&&!$e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Kt(e,t,o)):(ue&&r&&Zu(t),t.flags|=1,Me(e,t,n,o),t.child)}function tf(e,t,n,r,o){if(Be(n)){var i=!0;Es(t)}else i=!1;if(Mr(t,o),t.stateNode===null)ts(e,t),nm(t,n,r),Yl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=lt(u):(u=Be(n)?Zn:Oe.current,u=Xr(t,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Yd(t,s,r,u),cn=!1;var c=t.memoizedState;s.state=c,Ns(t,r,s,o),l=t.memoizedState,a!==r||c!==l||Ue.current||cn?(typeof f=="function"&&(Gl(t,n,f,r),l=t.memoizedState),(a=cn||Gd(t,n,a,r,c,l,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Ih(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:ft(t.type,a),s.props=u,d=t.pendingProps,c=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=lt(l):(l=Be(n)?Zn:Oe.current,l=Xr(t,l));var y=n.getDerivedStateFromProps;(f=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||c!==l)&&Yd(t,s,r,l),cn=!1,c=t.memoizedState,s.state=c,Ns(t,r,s,o);var x=t.memoizedState;a!==d||c!==x||Ue.current||cn?(typeof y=="function"&&(Gl(t,n,y,r),x=t.memoizedState),(u=cn||Gd(t,n,u,r,c,x,l)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),r=!1)}return Zl(e,t,n,r,i,o)}function Zl(e,t,n,r,o,i){am(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&$d(t,n,!1),Kt(e,t,i);r=t.stateNode,Rw.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Zr(t,e.child,null,i),t.child=Zr(t,null,a,i)):Me(e,t,a,i),t.memoizedState=r.state,o&&$d(t,n,!0),t.child}function lm(e){var t=e.stateNode;t.pendingContext?zd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&zd(e,t.context,!1),sc(e,t.containerInfo)}function nf(e,t,n,r,o){return qr(),ec(o),t.flags|=256,Me(e,t,n,r),t.child}var Jl={dehydrated:null,treeContext:null,retryLane:0};function eu(e){return{baseLanes:e,cachePool:null,transitions:null}}function um(e,t,n){var r=t.pendingProps,o=de.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),oe(de,o&1),e===null)return Ql(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=na(s,r,0,null),e=qn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=eu(n),t.memoizedState=Jl,e):hc(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return _w(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Rn(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Rn(a,i):(i=qn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?eu(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Jl,r}return i=e.child,e=i.sibling,r=Rn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function hc(e,t){return t=na({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Mi(e,t,n,r){return r!==null&&ec(r),Zr(t,e.child,null,n),e=hc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function _w(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ja(Error(R(422))),Mi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=na({mode:"visible",children:r.children},o,0,null),i=qn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Zr(t,e.child,null,s),t.child.memoizedState=eu(s),t.memoizedState=Jl,i);if(!(t.mode&1))return Mi(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(R(419)),r=Ja(i,r,void 0),Mi(e,t,s,r)}if(a=(s&e.childLanes)!==0,$e||a){if(r=Se,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Qt(e,o),yt(r,e,o,-1))}return xc(),r=Ja(Error(R(421))),Mi(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Vw.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Xe=kn(o.nextSibling),qe=t,ue=!0,vt=null,e!==null&&(ot[it++]=Ut,ot[it++]=Bt,ot[it++]=Jn,Ut=e.id,Bt=e.overflow,Jn=t),t=hc(t,r.children),t.flags|=4096,t)}function rf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Kl(e.return,t,n)}function el(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function cm(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Me(e,t,r.children,n),r=de.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&rf(e,n,t);else if(e.tag===19)rf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(oe(de,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ts(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),el(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ts(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}el(t,!0,n,null,i);break;case"together":el(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ts(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Kt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),tr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Rn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Rn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ow(e,t,n){switch(t.tag){case 3:lm(t),qr();break;case 5:Dh(t);break;case 1:Be(t.type)&&Es(t);break;case 4:sc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;oe(ks,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(oe(de,de.current&1),t.flags|=128,null):n&t.child.childLanes?um(e,t,n):(oe(de,de.current&1),e=Kt(e,t,n),e!==null?e.sibling:null);oe(de,de.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return cm(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),oe(de,de.current),r)break;return null;case 22:case 23:return t.lanes=0,sm(e,t,n)}return Kt(e,t,n)}var dm,tu,fm,pm;dm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};tu=function(){};fm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Hn(jt.current);var i=null;switch(n){case"input":o=Cl(e,o),r=Cl(e,r),i=[];break;case"select":o=pe({},o,{value:void 0}),r=pe({},r,{value:void 0}),i=[];break;case"textarea":o=Pl(e,o),r=Pl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=xs)}Tl(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&($o.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&($o.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ae("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};pm=function(e,t,n,r){n!==r&&(t.flags|=4)};function wo(e,t){if(!ue)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Te(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Aw(e,t,n){var r=t.pendingProps;switch(Ju(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Te(t),null;case 1:return Be(t.type)&&Ss(),Te(t),null;case 3:return r=t.stateNode,Jr(),le(Ue),le(Oe),lc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ai(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,vt!==null&&(uu(vt),vt=null))),tu(e,t),Te(t),null;case 5:ac(t);var o=Hn(Zo.current);if(n=t.type,e!==null&&t.stateNode!=null)fm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Te(t),null}if(e=Hn(jt.current),Ai(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[_t]=t,r[Xo]=i,e=(t.mode&1)!==0,n){case"dialog":ae("cancel",r),ae("close",r);break;case"iframe":case"object":case"embed":ae("load",r);break;case"video":case"audio":for(o=0;o<Po.length;o++)ae(Po[o],r);break;case"source":ae("error",r);break;case"img":case"image":case"link":ae("error",r),ae("load",r);break;case"details":ae("toggle",r);break;case"input":fd(r,i),ae("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ae("invalid",r);break;case"textarea":hd(r,i),ae("invalid",r)}Tl(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Oi(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Oi(r.textContent,a,e),o=["children",""+a]):$o.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&ae("scroll",r)}switch(n){case"input":Ci(r),pd(r,i,!0);break;case"textarea":Ci(r),md(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=xs)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Up(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[_t]=t,e[Xo]=r,dm(e,t,!1,!1),t.stateNode=e;e:{switch(s=Rl(n,r),n){case"dialog":ae("cancel",e),ae("close",e),o=r;break;case"iframe":case"object":case"embed":ae("load",e),o=r;break;case"video":case"audio":for(o=0;o<Po.length;o++)ae(Po[o],e);o=r;break;case"source":ae("error",e),o=r;break;case"img":case"image":case"link":ae("error",e),ae("load",e),o=r;break;case"details":ae("toggle",e),o=r;break;case"input":fd(e,r),o=Cl(e,r),ae("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=pe({},r,{value:void 0}),ae("invalid",e);break;case"textarea":hd(e,r),o=Pl(e,r),ae("invalid",e);break;default:o=r}Tl(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Wp(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Bp(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Uo(e,l):typeof l=="number"&&Uo(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&($o.hasOwnProperty(i)?l!=null&&i==="onScroll"&&ae("scroll",e):l!=null&&Fu(e,i,l,s))}switch(n){case"input":Ci(e),pd(e,r,!1);break;case"textarea":Ci(e),md(e);break;case"option":r.value!=null&&e.setAttribute("value",""+_n(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?_r(e,!!r.multiple,i,!1):r.defaultValue!=null&&_r(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=xs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Te(t),null;case 6:if(e&&t.stateNode!=null)pm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Hn(Zo.current),Hn(jt.current),Ai(t)){if(r=t.stateNode,n=t.memoizedProps,r[_t]=t,(i=r.nodeValue!==n)&&(e=qe,e!==null))switch(e.tag){case 3:Oi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Oi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[_t]=t,t.stateNode=r}return Te(t),null;case 13:if(le(de),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ue&&Xe!==null&&t.mode&1&&!(t.flags&128))Oh(),qr(),t.flags|=98560,i=!1;else if(i=Ai(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[_t]=t}else qr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Te(t),i=!1}else vt!==null&&(uu(vt),vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||de.current&1?we===0&&(we=3):xc())),t.updateQueue!==null&&(t.flags|=4),Te(t),null);case 4:return Jr(),tu(e,t),e===null&&Go(t.stateNode.containerInfo),Te(t),null;case 10:return rc(t.type._context),Te(t),null;case 17:return Be(t.type)&&Ss(),Te(t),null;case 19:if(le(de),i=t.memoizedState,i===null)return Te(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)wo(i,!1);else{if(we!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Ts(e),s!==null){for(t.flags|=128,wo(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return oe(de,de.current&1|2),t.child}e=e.sibling}i.tail!==null&&ve()>to&&(t.flags|=128,r=!0,wo(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ts(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),wo(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ue)return Te(t),null}else 2*ve()-i.renderingStartTime>to&&n!==1073741824&&(t.flags|=128,r=!0,wo(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ve(),t.sibling=null,n=de.current,oe(de,r?n&1|2:n&1),t):(Te(t),null);case 22:case 23:return wc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ge&1073741824&&(Te(t),t.subtreeFlags&6&&(t.flags|=8192)):Te(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function jw(e,t){switch(Ju(t),t.tag){case 1:return Be(t.type)&&Ss(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Jr(),le(Ue),le(Oe),lc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ac(t),null;case 13:if(le(de),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));qr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(de),null;case 4:return Jr(),null;case 10:return rc(t.type._context),null;case 22:case 23:return wc(),null;case 24:return null;default:return null}}var Ii=!1,_e=!1,Mw=typeof WeakSet=="function"?WeakSet:Set,I=null;function Pr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){me(e,t,r)}else n.current=null}function nu(e,t,n){try{n()}catch(r){me(e,t,r)}}var of=!1;function Iw(e,t){if(zl=gs,e=yh(),qu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,f=0,d=e,c=null;t:for(;;){for(var y;d!==n||o!==0&&d.nodeType!==3||(a=s+o),d!==i||r!==0&&d.nodeType!==3||(l=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(y=d.firstChild)!==null;)c=d,d=y;for(;;){if(d===e)break t;if(c===n&&++u===o&&(a=s),c===i&&++f===r&&(l=s),(y=d.nextSibling)!==null)break;d=c,c=d.parentNode}d=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for($l={focusedElem:e,selectionRange:n},gs=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var p=x.memoizedProps,S=x.memoizedState,m=t.stateNode,v=m.getSnapshotBeforeUpdate(t.elementType===t.type?p:ft(t.type,p),S);m.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var w=t.stateNode.containerInfo;w.nodeType===1?w.textContent="":w.nodeType===9&&w.documentElement&&w.removeChild(w.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(E){me(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return x=of,of=!1,x}function jo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&nu(t,n,i)}o=o.next}while(o!==r)}}function ea(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ru(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function hm(e){var t=e.alternate;t!==null&&(e.alternate=null,hm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[_t],delete t[Xo],delete t[Vl],delete t[gw],delete t[yw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mm(e){return e.tag===5||e.tag===3||e.tag===4}function sf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ou(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=xs));else if(r!==4&&(e=e.child,e!==null))for(ou(e,t,n),e=e.sibling;e!==null;)ou(e,t,n),e=e.sibling}function iu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(iu(e,t,n),e=e.sibling;e!==null;)iu(e,t,n),e=e.sibling}var Ce=null,mt=!1;function rn(e,t,n){for(n=n.child;n!==null;)vm(e,t,n),n=n.sibling}function vm(e,t,n){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(Qs,n)}catch{}switch(n.tag){case 5:_e||Pr(n,t);case 6:var r=Ce,o=mt;Ce=null,rn(e,t,n),Ce=r,mt=o,Ce!==null&&(mt?(e=Ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ce.removeChild(n.stateNode));break;case 18:Ce!==null&&(mt?(e=Ce,n=n.stateNode,e.nodeType===8?Ka(e.parentNode,n):e.nodeType===1&&Ka(e,n),Ho(e)):Ka(Ce,n.stateNode));break;case 4:r=Ce,o=mt,Ce=n.stateNode.containerInfo,mt=!0,rn(e,t,n),Ce=r,mt=o;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&nu(n,t,s),o=o.next}while(o!==r)}rn(e,t,n);break;case 1:if(!_e&&(Pr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){me(n,t,a)}rn(e,t,n);break;case 21:rn(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,rn(e,t,n),_e=r):rn(e,t,n);break;default:rn(e,t,n)}}function af(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Mw),t.forEach(function(r){var o=Ww.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function dt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:Ce=a.stateNode,mt=!1;break e;case 3:Ce=a.stateNode.containerInfo,mt=!0;break e;case 4:Ce=a.stateNode.containerInfo,mt=!0;break e}a=a.return}if(Ce===null)throw Error(R(160));vm(i,s,o),Ce=null,mt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){me(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)gm(t,e),t=t.sibling}function gm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(dt(t,e),kt(e),r&4){try{jo(3,e,e.return),ea(3,e)}catch(p){me(e,e.return,p)}try{jo(5,e,e.return)}catch(p){me(e,e.return,p)}}break;case 1:dt(t,e),kt(e),r&512&&n!==null&&Pr(n,n.return);break;case 5:if(dt(t,e),kt(e),r&512&&n!==null&&Pr(n,n.return),e.flags&32){var o=e.stateNode;try{Uo(o,"")}catch(p){me(e,e.return,p)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&zp(o,i),Rl(a,s);var u=Rl(a,i);for(s=0;s<l.length;s+=2){var f=l[s],d=l[s+1];f==="style"?Wp(o,d):f==="dangerouslySetInnerHTML"?Bp(o,d):f==="children"?Uo(o,d):Fu(o,f,d,u)}switch(a){case"input":bl(o,i);break;case"textarea":$p(o,i);break;case"select":var c=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?_r(o,!!i.multiple,y,!1):c!==!!i.multiple&&(i.defaultValue!=null?_r(o,!!i.multiple,i.defaultValue,!0):_r(o,!!i.multiple,i.multiple?[]:"",!1))}o[Xo]=i}catch(p){me(e,e.return,p)}}break;case 6:if(dt(t,e),kt(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(p){me(e,e.return,p)}}break;case 3:if(dt(t,e),kt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ho(t.containerInfo)}catch(p){me(e,e.return,p)}break;case 4:dt(t,e),kt(e);break;case 13:dt(t,e),kt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(gc=ve())),r&4&&af(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(u=_e)||f,dt(t,e),_e=u):dt(t,e),kt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(I=e,f=e.child;f!==null;){for(d=I=f;I!==null;){switch(c=I,y=c.child,c.tag){case 0:case 11:case 14:case 15:jo(4,c,c.return);break;case 1:Pr(c,c.return);var x=c.stateNode;if(typeof x.componentWillUnmount=="function"){r=c,n=c.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(p){me(r,n,p)}}break;case 5:Pr(c,c.return);break;case 22:if(c.memoizedState!==null){uf(d);continue}}y!==null?(y.return=c,I=y):uf(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Vp("display",s))}catch(p){me(e,e.return,p)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(p){me(e,e.return,p)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:dt(t,e),kt(e),r&4&&af(e);break;case 21:break;default:dt(t,e),kt(e)}}function kt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mm(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Uo(o,""),r.flags&=-33);var i=sf(e);iu(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=sf(e);ou(e,a,s);break;default:throw Error(R(161))}}catch(l){me(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Dw(e,t,n){I=e,ym(e)}function ym(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Ii;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||_e;a=Ii;var u=_e;if(Ii=s,(_e=l)&&!u)for(I=o;I!==null;)s=I,l=s.child,s.tag===22&&s.memoizedState!==null?cf(o):l!==null?(l.return=s,I=l):cf(o);for(;i!==null;)I=i,ym(i),i=i.sibling;I=o,Ii=a,_e=u}lf(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,I=i):lf(e)}}function lf(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||ea(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ft(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Hd(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Hd(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&Ho(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}_e||t.flags&512&&ru(t)}catch(c){me(t,t.return,c)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function uf(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function cf(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ea(4,t)}catch(l){me(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){me(t,o,l)}}var i=t.return;try{ru(t)}catch(l){me(t,i,l)}break;case 5:var s=t.return;try{ru(t)}catch(l){me(t,s,l)}}}catch(l){me(t,t.return,l)}if(t===e){I=null;break}var a=t.sibling;if(a!==null){a.return=t.return,I=a;break}I=t.return}}var Lw=Math.ceil,Os=Xt.ReactCurrentDispatcher,mc=Xt.ReactCurrentOwner,at=Xt.ReactCurrentBatchConfig,q=0,Se=null,ge=null,be=0,Ge=0,Nr=Fn(0),we=0,ni=null,tr=0,ta=0,vc=0,Mo=null,ze=null,gc=0,to=1/0,Ft=null,As=!1,su=null,Nn=null,Di=!1,xn=null,js=0,Io=0,au=null,ns=-1,rs=0;function De(){return q&6?ve():ns!==-1?ns:ns=ve()}function Tn(e){return e.mode&1?q&2&&be!==0?be&-be:xw.transition!==null?(rs===0&&(rs=nh()),rs):(e=ee,e!==0||(e=window.event,e=e===void 0?16:uh(e.type)),e):1}function yt(e,t,n,r){if(50<Io)throw Io=0,au=null,Error(R(185));ci(e,n,r),(!(q&2)||e!==Se)&&(e===Se&&(!(q&2)&&(ta|=n),we===4&&pn(e,be)),Ve(e,r),n===1&&q===0&&!(t.mode&1)&&(to=ve()+500,qs&&zn()))}function Ve(e,t){var n=e.callbackNode;x0(e,t);var r=vs(e,e===Se?be:0);if(r===0)n!==null&&yd(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&yd(n),t===1)e.tag===0?ww(df.bind(null,e)):Th(df.bind(null,e)),mw(function(){!(q&6)&&zn()}),n=null;else{switch(rh(r)){case 1:n=Vu;break;case 4:n=eh;break;case 16:n=ms;break;case 536870912:n=th;break;default:n=ms}n=Pm(n,wm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wm(e,t){if(ns=-1,rs=0,q&6)throw Error(R(327));var n=e.callbackNode;if(Ir()&&e.callbackNode!==n)return null;var r=vs(e,e===Se?be:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ms(e,r);else{t=r;var o=q;q|=2;var i=Sm();(Se!==e||be!==t)&&(Ft=null,to=ve()+500,Xn(e,t));do try{$w();break}catch(a){xm(e,a)}while(!0);nc(),Os.current=i,q=o,ge!==null?t=0:(Se=null,be=0,t=we)}if(t!==0){if(t===2&&(o=Ml(e),o!==0&&(r=o,t=lu(e,o))),t===1)throw n=ni,Xn(e,0),pn(e,r),Ve(e,ve()),n;if(t===6)pn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Fw(o)&&(t=Ms(e,r),t===2&&(i=Ml(e),i!==0&&(r=i,t=lu(e,i))),t===1))throw n=ni,Xn(e,0),pn(e,r),Ve(e,ve()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Bn(e,ze,Ft);break;case 3:if(pn(e,r),(r&130023424)===r&&(t=gc+500-ve(),10<t)){if(vs(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){De(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Bl(Bn.bind(null,e,ze,Ft),t);break}Bn(e,ze,Ft);break;case 4:if(pn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-gt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=ve()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Lw(r/1960))-r,10<r){e.timeoutHandle=Bl(Bn.bind(null,e,ze,Ft),r);break}Bn(e,ze,Ft);break;case 5:Bn(e,ze,Ft);break;default:throw Error(R(329))}}}return Ve(e,ve()),e.callbackNode===n?wm.bind(null,e):null}function lu(e,t){var n=Mo;return e.current.memoizedState.isDehydrated&&(Xn(e,t).flags|=256),e=Ms(e,t),e!==2&&(t=ze,ze=n,t!==null&&uu(t)),e}function uu(e){ze===null?ze=e:ze.push.apply(ze,e)}function Fw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!wt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pn(e,t){for(t&=~vc,t&=~ta,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-gt(t),r=1<<n;e[n]=-1,t&=~r}}function df(e){if(q&6)throw Error(R(327));Ir();var t=vs(e,0);if(!(t&1))return Ve(e,ve()),null;var n=Ms(e,t);if(e.tag!==0&&n===2){var r=Ml(e);r!==0&&(t=r,n=lu(e,r))}if(n===1)throw n=ni,Xn(e,0),pn(e,t),Ve(e,ve()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Bn(e,ze,Ft),Ve(e,ve()),null}function yc(e,t){var n=q;q|=1;try{return e(t)}finally{q=n,q===0&&(to=ve()+500,qs&&zn())}}function nr(e){xn!==null&&xn.tag===0&&!(q&6)&&Ir();var t=q;q|=1;var n=at.transition,r=ee;try{if(at.transition=null,ee=1,e)return e()}finally{ee=r,at.transition=n,q=t,!(q&6)&&zn()}}function wc(){Ge=Nr.current,le(Nr)}function Xn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,hw(n)),ge!==null)for(n=ge.return;n!==null;){var r=n;switch(Ju(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ss();break;case 3:Jr(),le(Ue),le(Oe),lc();break;case 5:ac(r);break;case 4:Jr();break;case 13:le(de);break;case 19:le(de);break;case 10:rc(r.type._context);break;case 22:case 23:wc()}n=n.return}if(Se=e,ge=e=Rn(e.current,null),be=Ge=t,we=0,ni=null,vc=ta=tr=0,ze=Mo=null,Wn!==null){for(t=0;t<Wn.length;t++)if(n=Wn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Wn=null}return e}function xm(e,t){do{var n=ge;try{if(nc(),Ji.current=_s,Rs){for(var r=fe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Rs=!1}if(er=0,xe=ye=fe=null,Ao=!1,Jo=0,mc.current=null,n===null||n.return===null){we=1,ni=t,ge=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=be,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,f=a,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var c=f.alternate;c?(f.updateQueue=c.updateQueue,f.memoizedState=c.memoizedState,f.lanes=c.lanes):(f.updateQueue=null,f.memoizedState=null)}var y=qd(s);if(y!==null){y.flags&=-257,Zd(y,s,a,i,t),y.mode&1&&Xd(i,u,t),t=y,l=u;var x=t.updateQueue;if(x===null){var p=new Set;p.add(l),t.updateQueue=p}else x.add(l);break e}else{if(!(t&1)){Xd(i,u,t),xc();break e}l=Error(R(426))}}else if(ue&&a.mode&1){var S=qd(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Zd(S,s,a,i,t),ec(eo(l,a));break e}}i=l=eo(l,a),we!==4&&(we=2),Mo===null?Mo=[i]:Mo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=rm(i,l,t);Wd(i,m);break e;case 1:a=l;var v=i.type,w=i.stateNode;if(!(i.flags&128)&&(typeof v.getDerivedStateFromError=="function"||w!==null&&typeof w.componentDidCatch=="function"&&(Nn===null||!Nn.has(w)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=om(i,a,t);Wd(i,E);break e}}i=i.return}while(i!==null)}Cm(n)}catch(C){t=C,ge===n&&n!==null&&(ge=n=n.return);continue}break}while(!0)}function Sm(){var e=Os.current;return Os.current=_s,e===null?_s:e}function xc(){(we===0||we===3||we===2)&&(we=4),Se===null||!(tr&268435455)&&!(ta&268435455)||pn(Se,be)}function Ms(e,t){var n=q;q|=2;var r=Sm();(Se!==e||be!==t)&&(Ft=null,Xn(e,t));do try{zw();break}catch(o){xm(e,o)}while(!0);if(nc(),q=n,Os.current=r,ge!==null)throw Error(R(261));return Se=null,be=0,we}function zw(){for(;ge!==null;)Em(ge)}function $w(){for(;ge!==null&&!d0();)Em(ge)}function Em(e){var t=km(e.alternate,e,Ge);e.memoizedProps=e.pendingProps,t===null?Cm(e):ge=t,mc.current=null}function Cm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=jw(n,t),n!==null){n.flags&=32767,ge=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{we=6,ge=null;return}}else if(n=Aw(n,t,Ge),n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);we===0&&(we=5)}function Bn(e,t,n){var r=ee,o=at.transition;try{at.transition=null,ee=1,Uw(e,t,n,r)}finally{at.transition=o,ee=r}return null}function Uw(e,t,n,r){do Ir();while(xn!==null);if(q&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(S0(e,i),e===Se&&(ge=Se=null,be=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Di||(Di=!0,Pm(ms,function(){return Ir(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=at.transition,at.transition=null;var s=ee;ee=1;var a=q;q|=4,mc.current=null,Iw(e,n),gm(n,e),aw($l),gs=!!zl,$l=zl=null,e.current=n,Dw(n),f0(),q=a,ee=s,at.transition=i}else e.current=n;if(Di&&(Di=!1,xn=e,js=o),i=e.pendingLanes,i===0&&(Nn=null),m0(n.stateNode),Ve(e,ve()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(As)throw As=!1,e=su,su=null,e;return js&1&&e.tag!==0&&Ir(),i=e.pendingLanes,i&1?e===au?Io++:(Io=0,au=e):Io=0,zn(),null}function Ir(){if(xn!==null){var e=rh(js),t=at.transition,n=ee;try{if(at.transition=null,ee=16>e?16:e,xn===null)var r=!1;else{if(e=xn,xn=null,js=0,q&6)throw Error(R(331));var o=q;for(q|=4,I=e.current;I!==null;){var i=I,s=i.child;if(I.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(I=u;I!==null;){var f=I;switch(f.tag){case 0:case 11:case 15:jo(8,f,i)}var d=f.child;if(d!==null)d.return=f,I=d;else for(;I!==null;){f=I;var c=f.sibling,y=f.return;if(hm(f),f===u){I=null;break}if(c!==null){c.return=y,I=c;break}I=y}}}var x=i.alternate;if(x!==null){var p=x.child;if(p!==null){x.child=null;do{var S=p.sibling;p.sibling=null,p=S}while(p!==null)}}I=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,I=s;else e:for(;I!==null;){if(i=I,i.flags&2048)switch(i.tag){case 0:case 11:case 15:jo(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,I=m;break e}I=i.return}}var v=e.current;for(I=v;I!==null;){s=I;var w=s.child;if(s.subtreeFlags&2064&&w!==null)w.return=s,I=w;else e:for(s=v;I!==null;){if(a=I,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ea(9,a)}}catch(C){me(a,a.return,C)}if(a===s){I=null;break e}var E=a.sibling;if(E!==null){E.return=a.return,I=E;break e}I=a.return}}if(q=o,zn(),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(Qs,e)}catch{}r=!0}return r}finally{ee=n,at.transition=t}}return!1}function ff(e,t,n){t=eo(n,t),t=rm(e,t,1),e=Pn(e,t,1),t=De(),e!==null&&(ci(e,1,t),Ve(e,t))}function me(e,t,n){if(e.tag===3)ff(e,e,n);else for(;t!==null;){if(t.tag===3){ff(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nn===null||!Nn.has(r))){e=eo(n,e),e=om(t,e,1),t=Pn(t,e,1),e=De(),t!==null&&(ci(t,1,e),Ve(t,e));break}}t=t.return}}function Bw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=De(),e.pingedLanes|=e.suspendedLanes&n,Se===e&&(be&n)===n&&(we===4||we===3&&(be&130023424)===be&&500>ve()-gc?Xn(e,0):vc|=n),Ve(e,t)}function bm(e,t){t===0&&(e.mode&1?(t=Pi,Pi<<=1,!(Pi&130023424)&&(Pi=4194304)):t=1);var n=De();e=Qt(e,t),e!==null&&(ci(e,t,n),Ve(e,n))}function Vw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),bm(e,n)}function Ww(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),bm(e,n)}var km;km=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ue.current)$e=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return $e=!1,Ow(e,t,n);$e=!!(e.flags&131072)}else $e=!1,ue&&t.flags&1048576&&Rh(t,bs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ts(e,t),e=t.pendingProps;var o=Xr(t,Oe.current);Mr(t,n),o=cc(null,t,r,e,o,n);var i=dc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Be(r)?(i=!0,Es(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,ic(t),o.updater=Js,t.stateNode=o,o._reactInternals=t,Yl(t,r,e,n),t=Zl(null,t,r,!0,i,n)):(t.tag=0,ue&&i&&Zu(t),Me(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ts(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Qw(r),e=ft(r,e),o){case 0:t=ql(null,t,r,e,n);break e;case 1:t=tf(null,t,r,e,n);break e;case 11:t=Jd(null,t,r,e,n);break e;case 14:t=ef(null,t,r,ft(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),ql(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),tf(e,t,r,o,n);case 3:e:{if(lm(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Ih(e,t),Ns(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=eo(Error(R(423)),t),t=nf(e,t,r,n,o);break e}else if(r!==o){o=eo(Error(R(424)),t),t=nf(e,t,r,n,o);break e}else for(Xe=kn(t.stateNode.containerInfo.firstChild),qe=t,ue=!0,vt=null,n=jh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(qr(),r===o){t=Kt(e,t,n);break e}Me(e,t,r,n)}t=t.child}return t;case 5:return Dh(t),e===null&&Ql(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ul(r,o)?s=null:i!==null&&Ul(r,i)&&(t.flags|=32),am(e,t),Me(e,t,s,n),t.child;case 6:return e===null&&Ql(t),null;case 13:return um(e,t,n);case 4:return sc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Zr(t,null,r,n):Me(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),Jd(e,t,r,o,n);case 7:return Me(e,t,t.pendingProps,n),t.child;case 8:return Me(e,t,t.pendingProps.children,n),t.child;case 12:return Me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,oe(ks,r._currentValue),r._currentValue=s,i!==null)if(wt(i.value,s)){if(i.children===o.children&&!Ue.current){t=Kt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Vt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?l.next=l:(l.next=f.next,f.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Kl(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Kl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Me(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Mr(t,n),o=lt(o),r=r(o),t.flags|=1,Me(e,t,r,n),t.child;case 14:return r=t.type,o=ft(r,t.pendingProps),o=ft(r.type,o),ef(e,t,r,o,n);case 15:return im(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),ts(e,t),t.tag=1,Be(r)?(e=!0,Es(t)):e=!1,Mr(t,n),nm(t,r,o),Yl(t,r,o,n),Zl(null,t,r,!0,e,n);case 19:return cm(e,t,n);case 22:return sm(e,t,n)}throw Error(R(156,t.tag))};function Pm(e,t){return Jp(e,t)}function Hw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,n,r){return new Hw(e,t,n,r)}function Sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Qw(e){if(typeof e=="function")return Sc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===$u)return 11;if(e===Uu)return 14}return 2}function Rn(e,t){var n=e.alternate;return n===null?(n=st(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function os(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Sc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case gr:return qn(n.children,o,i,t);case zu:s=8,o|=8;break;case wl:return e=st(12,n,t,o|2),e.elementType=wl,e.lanes=i,e;case xl:return e=st(13,n,t,o),e.elementType=xl,e.lanes=i,e;case Sl:return e=st(19,n,t,o),e.elementType=Sl,e.lanes=i,e;case Dp:return na(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Mp:s=10;break e;case Ip:s=9;break e;case $u:s=11;break e;case Uu:s=14;break e;case un:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=st(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function qn(e,t,n,r){return e=st(7,e,r,t),e.lanes=n,e}function na(e,t,n,r){return e=st(22,e,r,t),e.elementType=Dp,e.lanes=n,e.stateNode={isHidden:!1},e}function tl(e,t,n){return e=st(6,e,null,t),e.lanes=n,e}function nl(e,t,n){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Kw(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Da(0),this.expirationTimes=Da(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Da(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Ec(e,t,n,r,o,i,s,a,l){return e=new Kw(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=st(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ic(i),e}function Gw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:vr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Nm(e){if(!e)return On;e=e._reactInternals;e:{if(sr(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Be(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Be(n))return Nh(e,n,t)}return t}function Tm(e,t,n,r,o,i,s,a,l){return e=Ec(n,r,!0,e,o,i,s,a,l),e.context=Nm(null),n=e.current,r=De(),o=Tn(n),i=Vt(r,o),i.callback=t??null,Pn(n,i,o),e.current.lanes=o,ci(e,o,r),Ve(e,r),e}function ra(e,t,n,r){var o=t.current,i=De(),s=Tn(o);return n=Nm(n),t.context===null?t.context=n:t.pendingContext=n,t=Vt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Pn(o,t,s),e!==null&&(yt(e,o,s,i),Zi(e,o,s)),s}function Is(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function pf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Cc(e,t){pf(e,t),(e=e.alternate)&&pf(e,t)}function Yw(){return null}var Rm=typeof reportError=="function"?reportError:function(e){console.error(e)};function bc(e){this._internalRoot=e}oa.prototype.render=bc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));ra(e,t,null,null)};oa.prototype.unmount=bc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;nr(function(){ra(null,e,null,null)}),t[Ht]=null}};function oa(e){this._internalRoot=e}oa.prototype.unstable_scheduleHydration=function(e){if(e){var t=sh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<fn.length&&t!==0&&t<fn[n].priority;n++);fn.splice(n,0,e),n===0&&lh(e)}};function kc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ia(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function hf(){}function Xw(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Is(s);i.call(u)}}var s=Tm(t,r,e,0,null,!1,!1,"",hf);return e._reactRootContainer=s,e[Ht]=s.current,Go(e.nodeType===8?e.parentNode:e),nr(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Is(l);a.call(u)}}var l=Ec(e,0,!1,null,null,!1,!1,"",hf);return e._reactRootContainer=l,e[Ht]=l.current,Go(e.nodeType===8?e.parentNode:e),nr(function(){ra(t,l,n,r)}),l}function sa(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Is(s);a.call(l)}}ra(t,s,e,o)}else s=Xw(n,t,e,o,r);return Is(s)}oh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ko(t.pendingLanes);n!==0&&(Wu(t,n|1),Ve(t,ve()),!(q&6)&&(to=ve()+500,zn()))}break;case 13:nr(function(){var r=Qt(e,1);if(r!==null){var o=De();yt(r,e,1,o)}}),Cc(e,1)}};Hu=function(e){if(e.tag===13){var t=Qt(e,134217728);if(t!==null){var n=De();yt(t,e,134217728,n)}Cc(e,134217728)}};ih=function(e){if(e.tag===13){var t=Tn(e),n=Qt(e,t);if(n!==null){var r=De();yt(n,e,t,r)}Cc(e,t)}};sh=function(){return ee};ah=function(e,t){var n=ee;try{return ee=e,t()}finally{ee=n}};Ol=function(e,t,n){switch(t){case"input":if(bl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Xs(r);if(!o)throw Error(R(90));Fp(r),bl(r,o)}}}break;case"textarea":$p(e,n);break;case"select":t=n.value,t!=null&&_r(e,!!n.multiple,t,!1)}};Kp=yc;Gp=nr;var qw={usingClientEntryPoint:!1,Events:[fi,Sr,Xs,Hp,Qp,yc]},xo={findFiberByHostInstance:Vn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Zw={bundleType:xo.bundleType,version:xo.version,rendererPackageName:xo.rendererPackageName,rendererConfig:xo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=qp(e),e===null?null:e.stateNode},findFiberByHostInstance:xo.findFiberByHostInstance||Yw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Li=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Li.isDisabled&&Li.supportsFiber)try{Qs=Li.inject(Zw),At=Li}catch{}}tt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=qw;tt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!kc(t))throw Error(R(200));return Gw(e,t,null,n)};tt.createRoot=function(e,t){if(!kc(e))throw Error(R(299));var n=!1,r="",o=Rm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Ec(e,1,!1,null,null,n,!1,r,o),e[Ht]=t.current,Go(e.nodeType===8?e.parentNode:e),new bc(t)};tt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=qp(t),e=e===null?null:e.stateNode,e};tt.flushSync=function(e){return nr(e)};tt.hydrate=function(e,t,n){if(!ia(t))throw Error(R(200));return sa(null,e,t,!0,n)};tt.hydrateRoot=function(e,t,n){if(!kc(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Rm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Tm(t,null,e,1,n??null,o,!1,i,s),e[Ht]=t.current,Go(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new oa(t)};tt.render=function(e,t,n){if(!ia(t))throw Error(R(200));return sa(null,e,t,!1,n)};tt.unmountComponentAtNode=function(e){if(!ia(e))throw Error(R(40));return e._reactRootContainer?(nr(function(){sa(null,null,e,!1,function(){e._reactRootContainer=null,e[Ht]=null})}),!0):!1};tt.unstable_batchedUpdates=yc;tt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ia(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return sa(e,t,n,!1,r)};tt.version="18.3.1-next-f1338f8080-20240426";function _m(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(_m)}catch(e){console.error(e)}}_m(),_p.exports=tt;var hi=_p.exports;const Om=gp(hi);var Am,mf=hi;Am=mf.createRoot,mf.hydrateRoot;const Jw=1,ex=1e6;let rl=0;function tx(){return rl=(rl+1)%Number.MAX_SAFE_INTEGER,rl.toString()}const ol=new Map,vf=e=>{if(ol.has(e))return;const t=setTimeout(()=>{ol.delete(e),Do({type:"REMOVE_TOAST",toastId:e})},ex);ol.set(e,t)},nx=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Jw)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?vf(n):e.toasts.forEach(r=>{vf(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},is=[];let ss={toasts:[]};function Do(e){ss=nx(ss,e),is.forEach(t=>{t(ss)})}function rx({...e}){const t=tx(),n=o=>Do({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Do({type:"DISMISS_TOAST",toastId:t});return Do({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function ox(){const[e,t]=h.useState(ss);return h.useEffect(()=>(is.push(t),()=>{const n=is.indexOf(t);n>-1&&is.splice(n,1)}),[e]),{...e,toast:rx,dismiss:n=>Do({type:"DISMISS_TOAST",toastId:n})}}function X(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ix(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function jm(...e){return t=>e.forEach(n=>ix(n,t))}function Ae(...e){return h.useCallback(jm(...e),e)}function sx(e,t=[]){let n=[];function r(i,s){const a=h.createContext(s),l=n.length;n=[...n,s];function u(d){const{scope:c,children:y,...x}=d,p=(c==null?void 0:c[e][l])||a,S=h.useMemo(()=>x,Object.values(x));return g.jsx(p.Provider,{value:S,children:y})}function f(d,c){const y=(c==null?void 0:c[e][l])||a,x=h.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>h.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,ax(o,...t)]}function ax(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var no=h.forwardRef((e,t)=>{const{children:n,...r}=e,o=h.Children.toArray(n),i=o.find(lx);if(i){const s=i.props.children,a=o.map(l=>l===i?h.Children.count(s)>1?h.Children.only(null):h.isValidElement(s)?s.props.children:null:l);return g.jsx(cu,{...r,ref:t,children:h.isValidElement(s)?h.cloneElement(s,void 0,a):null})}return g.jsx(cu,{...r,ref:t,children:n})});no.displayName="Slot";var cu=h.forwardRef((e,t)=>{const{children:n,...r}=e;if(h.isValidElement(n)){const o=cx(n);return h.cloneElement(n,{...ux(r,n.props),ref:t?jm(t,o):o})}return h.Children.count(n)>1?h.Children.only(null):null});cu.displayName="SlotClone";var Mm=({children:e})=>g.jsx(g.Fragment,{children:e});function lx(e){return h.isValidElement(e)&&e.type===Mm}function ux(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function cx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Im(e){const t=e+"CollectionProvider",[n,r]=sx(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=y=>{const{scope:x,children:p}=y,S=O.useRef(null),m=O.useRef(new Map).current;return g.jsx(o,{scope:x,itemMap:m,collectionRef:S,children:p})};s.displayName=t;const a=e+"CollectionSlot",l=O.forwardRef((y,x)=>{const{scope:p,children:S}=y,m=i(a,p),v=Ae(x,m.collectionRef);return g.jsx(no,{ref:v,children:S})});l.displayName=a;const u=e+"CollectionItemSlot",f="data-radix-collection-item",d=O.forwardRef((y,x)=>{const{scope:p,children:S,...m}=y,v=O.useRef(null),w=Ae(x,v),E=i(u,p);return O.useEffect(()=>(E.itemMap.set(v,{ref:v,...m}),()=>void E.itemMap.delete(v))),g.jsx(no,{[f]:"",ref:w,children:S})});d.displayName=u;function c(y){const x=i(e+"CollectionConsumer",y);return O.useCallback(()=>{const S=x.collectionRef.current;if(!S)return[];const m=Array.from(S.querySelectorAll(`[${f}]`));return Array.from(x.itemMap.values()).sort((E,C)=>m.indexOf(E.ref.current)-m.indexOf(C.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:s,Slot:l,ItemSlot:d},c,r]}function dx(e,t){const n=h.createContext(t),r=i=>{const{children:s,...a}=i,l=h.useMemo(()=>a,Object.values(a));return g.jsx(n.Provider,{value:l,children:s})};r.displayName=e+"Provider";function o(i){const s=h.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function aa(e,t=[]){let n=[];function r(i,s){const a=h.createContext(s),l=n.length;n=[...n,s];const u=d=>{var m;const{scope:c,children:y,...x}=d,p=((m=c==null?void 0:c[e])==null?void 0:m[l])||a,S=h.useMemo(()=>x,Object.values(x));return g.jsx(p.Provider,{value:S,children:y})};u.displayName=i+"Provider";function f(d,c){var p;const y=((p=c==null?void 0:c[e])==null?void 0:p[l])||a,x=h.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,f]}const o=()=>{const i=n.map(s=>h.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,fx(o,...t)]}function fx(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var px=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],te=px.reduce((e,t)=>{const n=h.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?no:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Dm(e,t){e&&hi.flushSync(()=>e.dispatchEvent(t))}function et(e){const t=h.useRef(e);return h.useEffect(()=>{t.current=e}),h.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function hx(e,t=globalThis==null?void 0:globalThis.document){const n=et(e);h.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var mx="DismissableLayer",du="dismissableLayer.update",vx="dismissableLayer.pointerDownOutside",gx="dismissableLayer.focusOutside",gf,Lm=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),la=h.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=h.useContext(Lm),[f,d]=h.useState(null),c=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=h.useState({}),x=Ae(t,N=>d(N)),p=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),m=p.indexOf(S),v=f?p.indexOf(f):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,E=v>=m,C=wx(N=>{const P=N.target,M=[...u.branches].some(A=>A.contains(P));!E||M||(o==null||o(N),s==null||s(N),N.defaultPrevented||a==null||a())},c),k=xx(N=>{const P=N.target;[...u.branches].some(A=>A.contains(P))||(i==null||i(N),s==null||s(N),N.defaultPrevented||a==null||a())},c);return hx(N=>{v===u.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&a&&(N.preventDefault(),a()))},c),h.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(gf=c.body.style.pointerEvents,c.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),yf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(c.body.style.pointerEvents=gf)}},[f,c,n,u]),h.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),yf())},[f,u]),h.useEffect(()=>{const N=()=>y({});return document.addEventListener(du,N),()=>document.removeEventListener(du,N)},[]),g.jsx(te.div,{...l,ref:x,style:{pointerEvents:w?E?"auto":"none":void 0,...e.style},onFocusCapture:X(e.onFocusCapture,k.onFocusCapture),onBlurCapture:X(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:X(e.onPointerDownCapture,C.onPointerDownCapture)})});la.displayName=mx;var yx="DismissableLayerBranch",Fm=h.forwardRef((e,t)=>{const n=h.useContext(Lm),r=h.useRef(null),o=Ae(t,r);return h.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),g.jsx(te.div,{...e,ref:o})});Fm.displayName=yx;function wx(e,t=globalThis==null?void 0:globalThis.document){const n=et(e),r=h.useRef(!1),o=h.useRef(()=>{});return h.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){zm(vx,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function xx(e,t=globalThis==null?void 0:globalThis.document){const n=et(e),r=h.useRef(!1);return h.useEffect(()=>{const o=i=>{i.target&&!r.current&&zm(gx,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function yf(){const e=new CustomEvent(du);document.dispatchEvent(e)}function zm(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Dm(o,i):o.dispatchEvent(i)}var Sx=la,Ex=Fm,An=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{},Cx="Portal",Pc=h.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=h.useState(!1);An(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Om.createPortal(g.jsx(te.div,{...r,ref:t}),s):null});Pc.displayName=Cx;function bx(e,t){return h.useReducer((n,r)=>t[n][r]??n,e)}var ar=e=>{const{present:t,children:n}=e,r=kx(t),o=typeof n=="function"?n({present:r.isPresent}):h.Children.only(n),i=Ae(r.ref,Px(o));return typeof n=="function"||r.isPresent?h.cloneElement(o,{ref:i}):null};ar.displayName="Presence";function kx(e){const[t,n]=h.useState(),r=h.useRef({}),o=h.useRef(e),i=h.useRef("none"),s=e?"mounted":"unmounted",[a,l]=bx(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return h.useEffect(()=>{const u=Fi(r.current);i.current=a==="mounted"?u:"none"},[a]),An(()=>{const u=r.current,f=o.current;if(f!==e){const c=i.current,y=Fi(u);e?l("MOUNT"):y==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(f&&c!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),An(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=y=>{const p=Fi(r.current).includes(y.animationName);if(y.target===t&&p&&(l("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},c=y=>{y.target===t&&(i.current=Fi(r.current))};return t.addEventListener("animationstart",c),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",c),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:h.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Fi(e){return(e==null?void 0:e.animationName)||"none"}function Px(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function ua({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Nx({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=et(n),l=h.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&a(d)}else o(u)},[i,e,o,a]);return[s,l]}function Nx({defaultProp:e,onChange:t}){const n=h.useState(e),[r]=n,o=h.useRef(r),i=et(t);return h.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var Tx="VisuallyHidden",ca=h.forwardRef((e,t)=>g.jsx(te.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ca.displayName=Tx;var Rx=ca,Nc="ToastProvider",[Tc,_x,Ox]=Im("Toast"),[$m,Kk]=aa("Toast",[Ox]),[Ax,da]=$m(Nc),Um=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=h.useState(null),[u,f]=h.useState(0),d=h.useRef(!1),c=h.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Nc}\`. Expected non-empty \`string\`.`),g.jsx(Tc.Provider,{scope:t,children:g.jsx(Ax,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:h.useCallback(()=>f(y=>y+1),[]),onToastRemove:h.useCallback(()=>f(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:c,children:s})})};Um.displayName=Nc;var Bm="ToastViewport",jx=["F8"],fu="toast.viewportPause",pu="toast.viewportResume",Vm=h.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=jx,label:o="Notifications ({hotkey})",...i}=e,s=da(Bm,n),a=_x(n),l=h.useRef(null),u=h.useRef(null),f=h.useRef(null),d=h.useRef(null),c=Ae(t,d,s.onViewportChange),y=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=s.toastCount>0;h.useEffect(()=>{const S=m=>{var w;r.length!==0&&r.every(E=>m[E]||m.code===E)&&((w=d.current)==null||w.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),h.useEffect(()=>{const S=l.current,m=d.current;if(x&&S&&m){const v=()=>{if(!s.isClosePausedRef.current){const k=new CustomEvent(fu);m.dispatchEvent(k),s.isClosePausedRef.current=!0}},w=()=>{if(s.isClosePausedRef.current){const k=new CustomEvent(pu);m.dispatchEvent(k),s.isClosePausedRef.current=!1}},E=k=>{!S.contains(k.relatedTarget)&&w()},C=()=>{S.contains(document.activeElement)||w()};return S.addEventListener("focusin",v),S.addEventListener("focusout",E),S.addEventListener("pointermove",v),S.addEventListener("pointerleave",C),window.addEventListener("blur",v),window.addEventListener("focus",w),()=>{S.removeEventListener("focusin",v),S.removeEventListener("focusout",E),S.removeEventListener("pointermove",v),S.removeEventListener("pointerleave",C),window.removeEventListener("blur",v),window.removeEventListener("focus",w)}}},[x,s.isClosePausedRef]);const p=h.useCallback(({tabbingDirection:S})=>{const v=a().map(w=>{const E=w.ref.current,C=[E,...Qx(E)];return S==="forwards"?C:C.reverse()});return(S==="forwards"?v.reverse():v).flat()},[a]);return h.useEffect(()=>{const S=d.current;if(S){const m=v=>{var C,k,N;const w=v.altKey||v.ctrlKey||v.metaKey;if(v.key==="Tab"&&!w){const P=document.activeElement,M=v.shiftKey;if(v.target===S&&M){(C=u.current)==null||C.focus();return}const L=p({tabbingDirection:M?"backwards":"forwards"}),B=L.findIndex(j=>j===P);il(L.slice(B+1))?v.preventDefault():M?(k=u.current)==null||k.focus():(N=f.current)==null||N.focus()}};return S.addEventListener("keydown",m),()=>S.removeEventListener("keydown",m)}},[a,p]),g.jsxs(Ex,{ref:l,role:"region","aria-label":o.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&g.jsx(hu,{ref:u,onFocusFromOutsideViewport:()=>{const S=p({tabbingDirection:"forwards"});il(S)}}),g.jsx(Tc.Slot,{scope:n,children:g.jsx(te.ol,{tabIndex:-1,...i,ref:c})}),x&&g.jsx(hu,{ref:f,onFocusFromOutsideViewport:()=>{const S=p({tabbingDirection:"backwards"});il(S)}})]})});Vm.displayName=Bm;var Wm="ToastFocusProxy",hu=h.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=da(Wm,n);return g.jsx(ca,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});hu.displayName=Wm;var fa="Toast",Mx="toast.swipeStart",Ix="toast.swipeMove",Dx="toast.swipeCancel",Lx="toast.swipeEnd",Hm=h.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a=!0,l]=ua({prop:r,defaultProp:o,onChange:i});return g.jsx(ar,{present:n||a,children:g.jsx($x,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:et(e.onPause),onResume:et(e.onResume),onSwipeStart:X(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:X(e.onSwipeMove,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:X(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:X(e.onSwipeEnd,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),l(!1)})})})});Hm.displayName=fa;var[Fx,zx]=$m(fa,{onClose(){}}),$x=h.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:c,onSwipeEnd:y,...x}=e,p=da(fa,n),[S,m]=h.useState(null),v=Ae(t,j=>m(j)),w=h.useRef(null),E=h.useRef(null),C=o||p.duration,k=h.useRef(0),N=h.useRef(C),P=h.useRef(0),{onToastAdd:M,onToastRemove:A}=p,$=et(()=>{var Q;(S==null?void 0:S.contains(document.activeElement))&&((Q=p.viewport)==null||Q.focus()),s()}),L=h.useCallback(j=>{!j||j===1/0||(window.clearTimeout(P.current),k.current=new Date().getTime(),P.current=window.setTimeout($,j))},[$]);h.useEffect(()=>{const j=p.viewport;if(j){const Q=()=>{L(N.current),u==null||u()},z=()=>{const K=new Date().getTime()-k.current;N.current=N.current-K,window.clearTimeout(P.current),l==null||l()};return j.addEventListener(fu,z),j.addEventListener(pu,Q),()=>{j.removeEventListener(fu,z),j.removeEventListener(pu,Q)}}},[p.viewport,C,l,u,L]),h.useEffect(()=>{i&&!p.isClosePausedRef.current&&L(C)},[i,C,p.isClosePausedRef,L]),h.useEffect(()=>(M(),()=>A()),[M,A]);const B=h.useMemo(()=>S?Zm(S):null,[S]);return p.viewport?g.jsxs(g.Fragment,{children:[B&&g.jsx(Ux,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:B}),g.jsx(Fx,{scope:n,onClose:$,children:hi.createPortal(g.jsx(Tc.ItemSlot,{scope:n,children:g.jsx(Sx,{asChild:!0,onEscapeKeyDown:X(a,()=>{p.isFocusedToastEscapeKeyDownRef.current||$(),p.isFocusedToastEscapeKeyDownRef.current=!1}),children:g.jsx(te.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":p.swipeDirection,...x,ref:v,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:X(e.onKeyDown,j=>{j.key==="Escape"&&(a==null||a(j.nativeEvent),j.nativeEvent.defaultPrevented||(p.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:X(e.onPointerDown,j=>{j.button===0&&(w.current={x:j.clientX,y:j.clientY})}),onPointerMove:X(e.onPointerMove,j=>{if(!w.current)return;const Q=j.clientX-w.current.x,z=j.clientY-w.current.y,K=!!E.current,b=["left","right"].includes(p.swipeDirection),_=["left","up"].includes(p.swipeDirection)?Math.min:Math.max,F=b?_(0,Q):0,D=b?0:_(0,z),U=j.pointerType==="touch"?10:2,Y={x:F,y:D},ce={originalEvent:j,delta:Y};K?(E.current=Y,zi(Ix,d,ce,{discrete:!1})):wf(Y,p.swipeDirection,U)?(E.current=Y,zi(Mx,f,ce,{discrete:!1}),j.target.setPointerCapture(j.pointerId)):(Math.abs(Q)>U||Math.abs(z)>U)&&(w.current=null)}),onPointerUp:X(e.onPointerUp,j=>{const Q=E.current,z=j.target;if(z.hasPointerCapture(j.pointerId)&&z.releasePointerCapture(j.pointerId),E.current=null,w.current=null,Q){const K=j.currentTarget,b={originalEvent:j,delta:Q};wf(Q,p.swipeDirection,p.swipeThreshold)?zi(Lx,y,b,{discrete:!0}):zi(Dx,c,b,{discrete:!0}),K.addEventListener("click",_=>_.preventDefault(),{once:!0})}})})})}),p.viewport)})]}):null}),Ux=e=>{const{__scopeToast:t,children:n,...r}=e,o=da(fa,t),[i,s]=h.useState(!1),[a,l]=h.useState(!1);return Wx(()=>s(!0)),h.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:g.jsx(Pc,{asChild:!0,children:g.jsx(ca,{...r,children:i&&g.jsxs(g.Fragment,{children:[o.label," ",n]})})})},Bx="ToastTitle",Qm=h.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return g.jsx(te.div,{...r,ref:t})});Qm.displayName=Bx;var Vx="ToastDescription",Km=h.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return g.jsx(te.div,{...r,ref:t})});Km.displayName=Vx;var Gm="ToastAction",Ym=h.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?g.jsx(qm,{altText:n,asChild:!0,children:g.jsx(Rc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Gm}\`. Expected non-empty \`string\`.`),null)});Ym.displayName=Gm;var Xm="ToastClose",Rc=h.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=zx(Xm,n);return g.jsx(qm,{asChild:!0,children:g.jsx(te.button,{type:"button",...r,ref:t,onClick:X(e.onClick,o.onClose)})})});Rc.displayName=Xm;var qm=h.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return g.jsx(te.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Zm(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),Hx(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Zm(r))}}),t}function zi(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Dm(o,i):o.dispatchEvent(i)}var wf=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function Wx(e=()=>{}){const t=et(e);An(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function Hx(e){return e.nodeType===e.ELEMENT_NODE}function Qx(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function il(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var Kx=Um,Jm=Vm,ev=Hm,tv=Qm,nv=Km,rv=Ym,ov=Rc;function iv(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=iv(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function sv(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=iv(e))&&(r&&(r+=" "),r+=t);return r}const xf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Sf=sv,pa=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Sf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],d=i==null?void 0:i[u];if(f===null)return null;const c=xf(f)||xf(d);return o[u][c]}),a=n&&Object.entries(n).reduce((u,f)=>{let[d,c]=f;return c===void 0||(u[d]=c),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:d,className:c,...y}=f;return Object.entries(y).every(x=>{let[p,S]=x;return Array.isArray(S)?S.includes({...i,...a}[p]):{...i,...a}[p]===S})?[...u,d,c]:u},[]);return Sf(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gx=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),av=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xx=h.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...a},l)=>h.createElement("svg",{ref:l,...Yx,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:av("lucide",o),...a},[...s.map(([u,f])=>h.createElement(u,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=(e,t)=>{const n=h.forwardRef(({className:r,...o},i)=>h.createElement(Xx,{ref:i,iconNode:t,className:av(`lucide-${Gx(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qx=qt("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lv=qt("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ef=qt("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=qt("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zx=qt("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jx=qt("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=qt("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uv=qt("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cv=qt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),_c="-",t1=e=>{const t=r1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const a=s.split(_c);return a[0]===""&&a.length!==1&&a.shift(),dv(a,t)||n1(s)},getConflictingClassGroupIds:(s,a)=>{const l=n[s]||[];return a&&r[s]?[...l,...r[s]]:l}}},dv=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?dv(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(_c);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId},Cf=/^\[(.+)\]$/,n1=e=>{if(Cf.test(e)){const t=Cf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},r1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return i1(Object.entries(e.classGroups),n).forEach(([i,s])=>{vu(s,r,i,t)}),r},vu=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:bf(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(o1(o)){vu(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{vu(s,bf(t,i),n,r)})})},bf=(e,t)=>{let n=e;return t.split(_c).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},o1=e=>e.isThemeGetter,i1=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e,s1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},fv="!",a1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=a=>{const l=[];let u=0,f=0,d;for(let S=0;S<a.length;S++){let m=a[S];if(u===0){if(m===o&&(r||a.slice(S,S+i)===t)){l.push(a.slice(f,S)),f=S+i;continue}if(m==="/"){d=S;continue}}m==="["?u++:m==="]"&&u--}const c=l.length===0?a:a.substring(f),y=c.startsWith(fv),x=y?c.substring(1):c,p=d&&d>f?d-f:void 0;return{modifiers:l,hasImportantModifier:y,baseClassName:x,maybePostfixModifierPosition:p}};return n?a=>n({className:a,parseClassName:s}):s},l1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},u1=e=>({cache:s1(e.cacheSize),parseClassName:a1(e),...t1(e)}),c1=/\s+/,d1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(c1);let a="";for(let l=s.length-1;l>=0;l-=1){const u=s[l],{modifiers:f,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:y}=n(u);let x=!!y,p=r(x?c.substring(0,y):c);if(!p){if(!x){a=u+(a.length>0?" "+a:a);continue}if(p=r(c),!p){a=u+(a.length>0?" "+a:a);continue}x=!1}const S=l1(f).join(":"),m=d?S+fv:S,v=m+p;if(i.includes(v))continue;i.push(v);const w=o(p,x);for(let E=0;E<w.length;++E){const C=w[E];i.push(m+C)}a=u+(a.length>0?" "+a:a)}return a};function f1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=pv(t))&&(r&&(r+=" "),r+=n);return r}const pv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=pv(e[r]))&&(n&&(n+=" "),n+=t);return n};function p1(e,...t){let n,r,o,i=s;function s(l){const u=t.reduce((f,d)=>d(f),e());return n=u1(u),r=n.cache.get,o=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const f=d1(l,n);return o(l,f),f}return function(){return i(f1.apply(null,arguments))}}const se=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},hv=/^\[(?:([a-z-]+):)?(.+)\]$/i,h1=/^\d+\/\d+$/,m1=new Set(["px","full","screen"]),v1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,w1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,x1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Dt=e=>Dr(e)||m1.has(e)||h1.test(e),on=e=>lo(e,"length",T1),Dr=e=>!!e&&!Number.isNaN(Number(e)),sl=e=>lo(e,"number",Dr),So=e=>!!e&&Number.isInteger(Number(e)),S1=e=>e.endsWith("%")&&Dr(e.slice(0,-1)),W=e=>hv.test(e),sn=e=>v1.test(e),E1=new Set(["length","size","percentage"]),C1=e=>lo(e,E1,mv),b1=e=>lo(e,"position",mv),k1=new Set(["image","url"]),P1=e=>lo(e,k1,_1),N1=e=>lo(e,"",R1),Eo=()=>!0,lo=(e,t,n)=>{const r=hv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},T1=e=>g1.test(e)&&!y1.test(e),mv=()=>!1,R1=e=>w1.test(e),_1=e=>x1.test(e),O1=()=>{const e=se("colors"),t=se("spacing"),n=se("blur"),r=se("brightness"),o=se("borderColor"),i=se("borderRadius"),s=se("borderSpacing"),a=se("borderWidth"),l=se("contrast"),u=se("grayscale"),f=se("hueRotate"),d=se("invert"),c=se("gap"),y=se("gradientColorStops"),x=se("gradientColorStopPositions"),p=se("inset"),S=se("margin"),m=se("opacity"),v=se("padding"),w=se("saturate"),E=se("scale"),C=se("sepia"),k=se("skew"),N=se("space"),P=se("translate"),M=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto",W,t],L=()=>[W,t],B=()=>["",Dt,on],j=()=>["auto",Dr,W],Q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],z=()=>["solid","dashed","dotted","double","none"],K=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>["start","end","center","between","around","evenly","stretch"],_=()=>["","0",W],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[Dr,W];return{cacheSize:500,separator:":",theme:{colors:[Eo],spacing:[Dt,on],blur:["none","",sn,W],brightness:D(),borderColor:[e],borderRadius:["none","","full",sn,W],borderSpacing:L(),borderWidth:B(),contrast:D(),grayscale:_(),hueRotate:D(),invert:_(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[S1,on],inset:$(),margin:$(),opacity:D(),padding:L(),saturate:D(),scale:D(),sepia:_(),skew:D(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[sn]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Q(),W]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",So,W]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",So,W]}],"grid-cols":[{"grid-cols":[Eo]}],"col-start-end":[{col:["auto",{span:["full",So,W]},W]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[Eo]}],"row-start-end":[{row:["auto",{span:[So,W]},W]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...b()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...b(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...b(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,t]}],"min-w":[{"min-w":[W,t,"min","max","fit"]}],"max-w":[{"max-w":[W,t,"none","full","min","max","fit","prose",{screen:[sn]},sn]}],h:[{h:[W,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,t,"auto","min","max","fit"]}],"font-size":[{text:["base",sn,on]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",sl]}],"font-family":[{font:[Eo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",Dr,sl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Dt,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Dt,on]}],"underline-offset":[{"underline-offset":["auto",Dt,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Q(),b1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",C1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},P1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...z()]}],"outline-offset":[{"outline-offset":[Dt,W]}],"outline-w":[{outline:[Dt,on]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Dt,on]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",sn,N1]}],"shadow-color":[{shadow:[Eo]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...K(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":K()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",sn,W]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[d]}],saturate:[{saturate:[w]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[E]}],"scale-x":[{"scale-x":[E]}],"scale-y":[{"scale-y":[E]}],rotate:[{rotate:[So,W]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Dt,on,sl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},A1=p1(O1);function ne(...e){return A1(sv(e))}const j1=Kx,vv=h.forwardRef(({className:e,...t},n)=>g.jsx(Jm,{ref:n,className:ne("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));vv.displayName=Jm.displayName;const M1=pa("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),gv=h.forwardRef(({className:e,variant:t,...n},r)=>g.jsx(ev,{ref:r,className:ne(M1({variant:t}),e),...n}));gv.displayName=ev.displayName;const I1=h.forwardRef(({className:e,...t},n)=>g.jsx(rv,{ref:n,className:ne("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));I1.displayName=rv.displayName;const yv=h.forwardRef(({className:e,...t},n)=>g.jsx(ov,{ref:n,className:ne("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:g.jsx(cv,{className:"h-4 w-4"})}));yv.displayName=ov.displayName;const wv=h.forwardRef(({className:e,...t},n)=>g.jsx(tv,{ref:n,className:ne("text-sm font-semibold",e),...t}));wv.displayName=tv.displayName;const xv=h.forwardRef(({className:e,...t},n)=>g.jsx(nv,{ref:n,className:ne("text-sm opacity-90",e),...t}));xv.displayName=nv.displayName;function D1(){const{toasts:e}=ox();return g.jsxs(j1,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return g.jsxs(gv,{...i,children:[g.jsxs("div",{className:"grid gap-1",children:[n&&g.jsx(wv,{children:n}),r&&g.jsx(xv,{children:r})]}),o,g.jsx(yv,{})]},t)}),g.jsx(vv,{})]})}var kf=["light","dark"],L1="(prefers-color-scheme: dark)",F1=h.createContext(void 0),z1={setTheme:e=>{},themes:[]},$1=()=>{var e;return(e=h.useContext(F1))!=null?e:z1};h.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:i,value:s,attrs:a,nonce:l})=>{let u=i==="system",f=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${a.map(x=>`'${x}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,d=o?kf.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",c=(x,p=!1,S=!0)=>{let m=s?s[x]:x,v=p?x+"|| ''":`'${m}'`,w="";return o&&S&&!p&&kf.includes(x)&&(w+=`d.style.colorScheme = '${x}';`),n==="class"?p||m?w+=`c.add(${v})`:w+="null":m&&(w+=`d[s](n,${v})`),w},y=e?`!function(){${f}${c(e)}}()`:r?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${L1}',m=window.matchMedia(t);if(m.media!==t||m.matches){${c("dark")}}else{${c("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}${u?"":"else{"+c(i,!1,!1)+"}"}${d}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}else{${c(i,!1,!1)};}${d}}catch(t){}}();`;return h.createElement("script",{nonce:l,dangerouslySetInnerHTML:{__html:y}})});var U1=e=>{switch(e){case"success":return W1;case"info":return Q1;case"warning":return H1;case"error":return K1;default:return null}},B1=Array(12).fill(0),V1=({visible:e})=>O.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},O.createElement("div",{className:"sonner-spinner"},B1.map((t,n)=>O.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),W1=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),H1=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),Q1=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),K1=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),G1=()=>{let[e,t]=O.useState(document.hidden);return O.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},gu=1,Y1=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:gu++,i=this.toasts.find(a=>a.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:s,title:n}):a):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async i=>{if(q1(i)&&!i.ok){o=!1;let s=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,a=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:a})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?await t.success(i):t.success,a=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:s,description:a})}}).catch(async i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?await t.error(i):t.error,a=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:s,description:a})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||gu++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Ke=new Y1,X1=(e,t)=>{let n=(t==null?void 0:t.id)||gu++;return Ke.addToast({title:e,...t,id:n}),n},q1=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Z1=X1,J1=()=>Ke.toasts,dn=Object.assign(Z1,{success:Ke.success,info:Ke.info,warning:Ke.warning,error:Ke.error,custom:Ke.custom,message:Ke.message,promise:Ke.promise,dismiss:Ke.dismiss,loading:Ke.loading},{getHistory:J1});function eS(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}eS(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function $i(e){return e.label!==void 0}var tS=3,nS="32px",rS=4e3,oS=356,iS=14,sS=20,aS=200;function lS(...e){return e.filter(Boolean).join(" ")}var uS=e=>{var t,n,r,o,i,s,a,l,u,f;let{invert:d,toast:c,unstyled:y,interacting:x,setHeights:p,visibleToasts:S,heights:m,index:v,toasts:w,expanded:E,removeToast:C,defaultRichColors:k,closeButton:N,style:P,cancelButtonStyle:M,actionButtonStyle:A,className:$="",descriptionClassName:L="",duration:B,position:j,gap:Q,loadingIcon:z,expandByDefault:K,classNames:b,icons:_,closeButtonAriaLabel:F="Close toast",pauseWhenPageIsHidden:D,cn:U}=e,[Y,ce]=O.useState(!1),[He,J]=O.useState(!1),[ct,Zt]=O.useState(!1),[Jt,en]=O.useState(!1),[vi,lr]=O.useState(0),[$n,fo]=O.useState(0),gi=O.useRef(null),tn=O.useRef(null),Pa=v===0,Na=v+1<=S,Ee=c.type,ur=c.dismissible!==!1,xy=c.className||"",Sy=c.descriptionClassName||"",yi=O.useMemo(()=>m.findIndex(V=>V.toastId===c.id)||0,[m,c.id]),Ey=O.useMemo(()=>{var V;return(V=c.closeButton)!=null?V:N},[c.closeButton,N]),ed=O.useMemo(()=>c.duration||B||rS,[c.duration,B]),Ta=O.useRef(0),cr=O.useRef(0),td=O.useRef(0),dr=O.useRef(null),[nd,Cy]=j.split("-"),rd=O.useMemo(()=>m.reduce((V,ie,re)=>re>=yi?V:V+ie.height,0),[m,yi]),od=G1(),by=c.invert||d,Ra=Ee==="loading";cr.current=O.useMemo(()=>yi*Q+rd,[yi,rd]),O.useEffect(()=>{ce(!0)},[]),O.useLayoutEffect(()=>{if(!Y)return;let V=tn.current,ie=V.style.height;V.style.height="auto";let re=V.getBoundingClientRect().height;V.style.height=ie,fo(re),p(Ct=>Ct.find(bt=>bt.toastId===c.id)?Ct.map(bt=>bt.toastId===c.id?{...bt,height:re}:bt):[{toastId:c.id,height:re,position:c.position},...Ct])},[Y,c.title,c.description,p,c.id]);let nn=O.useCallback(()=>{J(!0),lr(cr.current),p(V=>V.filter(ie=>ie.toastId!==c.id)),setTimeout(()=>{C(c)},aS)},[c,C,p,cr]);O.useEffect(()=>{if(c.promise&&Ee==="loading"||c.duration===1/0||c.type==="loading")return;let V,ie=ed;return E||x||D&&od?(()=>{if(td.current<Ta.current){let re=new Date().getTime()-Ta.current;ie=ie-re}td.current=new Date().getTime()})():ie!==1/0&&(Ta.current=new Date().getTime(),V=setTimeout(()=>{var re;(re=c.onAutoClose)==null||re.call(c,c),nn()},ie)),()=>clearTimeout(V)},[E,x,K,c,ed,nn,c.promise,Ee,D,od]),O.useEffect(()=>{let V=tn.current;if(V){let ie=V.getBoundingClientRect().height;return fo(ie),p(re=>[{toastId:c.id,height:ie,position:c.position},...re]),()=>p(re=>re.filter(Ct=>Ct.toastId!==c.id))}},[p,c.id]),O.useEffect(()=>{c.delete&&nn()},[nn,c.delete]);function ky(){return _!=null&&_.loading?O.createElement("div",{className:"sonner-loader","data-visible":Ee==="loading"},_.loading):z?O.createElement("div",{className:"sonner-loader","data-visible":Ee==="loading"},z):O.createElement(V1,{visible:Ee==="loading"})}return O.createElement("li",{"aria-live":c.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:tn,className:U($,xy,b==null?void 0:b.toast,(t=c==null?void 0:c.classNames)==null?void 0:t.toast,b==null?void 0:b.default,b==null?void 0:b[Ee],(n=c==null?void 0:c.classNames)==null?void 0:n[Ee]),"data-sonner-toast":"","data-rich-colors":(r=c.richColors)!=null?r:k,"data-styled":!(c.jsx||c.unstyled||y),"data-mounted":Y,"data-promise":!!c.promise,"data-removed":He,"data-visible":Na,"data-y-position":nd,"data-x-position":Cy,"data-index":v,"data-front":Pa,"data-swiping":ct,"data-dismissible":ur,"data-type":Ee,"data-invert":by,"data-swipe-out":Jt,"data-expanded":!!(E||K&&Y),style:{"--index":v,"--toasts-before":v,"--z-index":w.length-v,"--offset":`${He?vi:cr.current}px`,"--initial-height":K?"auto":`${$n}px`,...P,...c.style},onPointerDown:V=>{Ra||!ur||(gi.current=new Date,lr(cr.current),V.target.setPointerCapture(V.pointerId),V.target.tagName!=="BUTTON"&&(Zt(!0),dr.current={x:V.clientX,y:V.clientY}))},onPointerUp:()=>{var V,ie,re,Ct;if(Jt||!ur)return;dr.current=null;let bt=Number(((V=tn.current)==null?void 0:V.style.getPropertyValue("--swipe-amount").replace("px",""))||0),wi=new Date().getTime()-((ie=gi.current)==null?void 0:ie.getTime()),Py=Math.abs(bt)/wi;if(Math.abs(bt)>=sS||Py>.11){lr(cr.current),(re=c.onDismiss)==null||re.call(c,c),nn(),en(!0);return}(Ct=tn.current)==null||Ct.style.setProperty("--swipe-amount","0px"),Zt(!1)},onPointerMove:V=>{var ie;if(!dr.current||!ur)return;let re=V.clientY-dr.current.y,Ct=V.clientX-dr.current.x,bt=(nd==="top"?Math.min:Math.max)(0,re),wi=V.pointerType==="touch"?10:2;Math.abs(bt)>wi?(ie=tn.current)==null||ie.style.setProperty("--swipe-amount",`${re}px`):Math.abs(Ct)>wi&&(dr.current=null)}},Ey&&!c.jsx?O.createElement("button",{"aria-label":F,"data-disabled":Ra,"data-close-button":!0,onClick:Ra||!ur?()=>{}:()=>{var V;nn(),(V=c.onDismiss)==null||V.call(c,c)},className:U(b==null?void 0:b.closeButton,(o=c==null?void 0:c.classNames)==null?void 0:o.closeButton)},O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},O.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),O.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,c.jsx||O.isValidElement(c.title)?c.jsx||c.title:O.createElement(O.Fragment,null,Ee||c.icon||c.promise?O.createElement("div",{"data-icon":"",className:U(b==null?void 0:b.icon,(i=c==null?void 0:c.classNames)==null?void 0:i.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||ky():null,c.type!=="loading"?c.icon||(_==null?void 0:_[Ee])||U1(Ee):null):null,O.createElement("div",{"data-content":"",className:U(b==null?void 0:b.content,(s=c==null?void 0:c.classNames)==null?void 0:s.content)},O.createElement("div",{"data-title":"",className:U(b==null?void 0:b.title,(a=c==null?void 0:c.classNames)==null?void 0:a.title)},c.title),c.description?O.createElement("div",{"data-description":"",className:U(L,Sy,b==null?void 0:b.description,(l=c==null?void 0:c.classNames)==null?void 0:l.description)},c.description):null),O.isValidElement(c.cancel)?c.cancel:c.cancel&&$i(c.cancel)?O.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||M,onClick:V=>{var ie,re;$i(c.cancel)&&ur&&((re=(ie=c.cancel).onClick)==null||re.call(ie,V),nn())},className:U(b==null?void 0:b.cancelButton,(u=c==null?void 0:c.classNames)==null?void 0:u.cancelButton)},c.cancel.label):null,O.isValidElement(c.action)?c.action:c.action&&$i(c.action)?O.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||A,onClick:V=>{var ie,re;$i(c.action)&&(V.defaultPrevented||((re=(ie=c.action).onClick)==null||re.call(ie,V),nn()))},className:U(b==null?void 0:b.actionButton,(f=c==null?void 0:c.classNames)==null?void 0:f.actionButton)},c.action.label):null))};function Pf(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var cS=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:a,theme:l="light",richColors:u,duration:f,style:d,visibleToasts:c=tS,toastOptions:y,dir:x=Pf(),gap:p=iS,loadingIcon:S,icons:m,containerAriaLabel:v="Notifications",pauseWhenPageIsHidden:w,cn:E=lS}=e,[C,k]=O.useState([]),N=O.useMemo(()=>Array.from(new Set([n].concat(C.filter(D=>D.position).map(D=>D.position)))),[C,n]),[P,M]=O.useState([]),[A,$]=O.useState(!1),[L,B]=O.useState(!1),[j,Q]=O.useState(l!=="system"?l:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),z=O.useRef(null),K=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=O.useRef(null),_=O.useRef(!1),F=O.useCallback(D=>{var U;(U=C.find(Y=>Y.id===D.id))!=null&&U.delete||Ke.dismiss(D.id),k(Y=>Y.filter(({id:ce})=>ce!==D.id))},[C]);return O.useEffect(()=>Ke.subscribe(D=>{if(D.dismiss){k(U=>U.map(Y=>Y.id===D.id?{...Y,delete:!0}:Y));return}setTimeout(()=>{Om.flushSync(()=>{k(U=>{let Y=U.findIndex(ce=>ce.id===D.id);return Y!==-1?[...U.slice(0,Y),{...U[Y],...D},...U.slice(Y+1)]:[D,...U]})})})}),[]),O.useEffect(()=>{if(l!=="system"){Q(l);return}l==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?Q("dark"):Q("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:D})=>{Q(D?"dark":"light")})},[l]),O.useEffect(()=>{C.length<=1&&$(!1)},[C]),O.useEffect(()=>{let D=U=>{var Y,ce;r.every(He=>U[He]||U.code===He)&&($(!0),(Y=z.current)==null||Y.focus()),U.code==="Escape"&&(document.activeElement===z.current||(ce=z.current)!=null&&ce.contains(document.activeElement))&&$(!1)};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[r]),O.useEffect(()=>{if(z.current)return()=>{b.current&&(b.current.focus({preventScroll:!0}),b.current=null,_.current=!1)}},[z.current]),C.length?O.createElement("section",{"aria-label":`${v} ${K}`,tabIndex:-1},N.map((D,U)=>{var Y;let[ce,He]=D.split("-");return O.createElement("ol",{key:D,dir:x==="auto"?Pf():x,tabIndex:-1,ref:z,className:s,"data-sonner-toaster":!0,"data-theme":j,"data-y-position":ce,"data-x-position":He,style:{"--front-toast-height":`${((Y=P[0])==null?void 0:Y.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||nS,"--width":`${oS}px`,"--gap":`${p}px`,...d},onBlur:J=>{_.current&&!J.currentTarget.contains(J.relatedTarget)&&(_.current=!1,b.current&&(b.current.focus({preventScroll:!0}),b.current=null))},onFocus:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||_.current||(_.current=!0,b.current=J.relatedTarget)},onMouseEnter:()=>$(!0),onMouseMove:()=>$(!0),onMouseLeave:()=>{L||$(!1)},onPointerDown:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||B(!0)},onPointerUp:()=>B(!1)},C.filter(J=>!J.position&&U===0||J.position===D).map((J,ct)=>{var Zt,Jt;return O.createElement(uS,{key:J.id,icons:m,index:ct,toast:J,defaultRichColors:u,duration:(Zt=y==null?void 0:y.duration)!=null?Zt:f,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:t,visibleToasts:c,closeButton:(Jt=y==null?void 0:y.closeButton)!=null?Jt:i,interacting:L,position:D,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:F,toasts:C.filter(en=>en.position==J.position),heights:P.filter(en=>en.position==J.position),setHeights:M,expandByDefault:o,gap:p,loadingIcon:S,expanded:A,pauseWhenPageIsHidden:w,cn:E})}))})):null};const dS=({...e})=>{const{theme:t="system"}=$1();return g.jsx(cS,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var fS=Tp.useId||(()=>{}),pS=0;function Lo(e){const[t,n]=h.useState(fS());return An(()=>{e||n(r=>r??String(pS++))},[e]),e||(t?`radix-${t}`:"")}const hS=["top","right","bottom","left"],jn=Math.min,Ye=Math.max,Ds=Math.round,Ui=Math.floor,Mn=e=>({x:e,y:e}),mS={left:"right",right:"left",bottom:"top",top:"bottom"},vS={start:"end",end:"start"};function yu(e,t,n){return Ye(e,jn(t,n))}function Gt(e,t){return typeof e=="function"?e(t):e}function Yt(e){return e.split("-")[0]}function uo(e){return e.split("-")[1]}function Oc(e){return e==="x"?"y":"x"}function Ac(e){return e==="y"?"height":"width"}function In(e){return["top","bottom"].includes(Yt(e))?"y":"x"}function jc(e){return Oc(In(e))}function gS(e,t,n){n===void 0&&(n=!1);const r=uo(e),o=jc(e),i=Ac(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Ls(s)),[s,Ls(s)]}function yS(e){const t=Ls(e);return[wu(e),t,wu(t)]}function wu(e){return e.replace(/start|end/g,t=>vS[t])}function wS(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function xS(e,t,n,r){const o=uo(e);let i=wS(Yt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(wu)))),i}function Ls(e){return e.replace(/left|right|bottom|top/g,t=>mS[t])}function SS(e){return{top:0,right:0,bottom:0,left:0,...e}}function Sv(e){return typeof e!="number"?SS(e):{top:e,right:e,bottom:e,left:e}}function Fs(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Nf(e,t,n){let{reference:r,floating:o}=e;const i=In(t),s=jc(t),a=Ac(s),l=Yt(t),u=i==="y",f=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,c=r[a]/2-o[a]/2;let y;switch(l){case"top":y={x:f,y:r.y-o.height};break;case"bottom":y={x:f,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:d};break;case"left":y={x:r.x-o.width,y:d};break;default:y={x:r.x,y:r.y}}switch(uo(t)){case"start":y[s]-=c*(n&&u?-1:1);break;case"end":y[s]+=c*(n&&u?-1:1);break}return y}const ES=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:d}=Nf(u,r,l),c=r,y={},x=0;for(let p=0;p<a.length;p++){const{name:S,fn:m}=a[p],{x:v,y:w,data:E,reset:C}=await m({x:f,y:d,initialPlacement:r,placement:c,strategy:o,middlewareData:y,rects:u,platform:s,elements:{reference:e,floating:t}});f=v??f,d=w??d,y={...y,[S]:{...y[S],...E}},C&&x<=50&&(x++,typeof C=="object"&&(C.placement&&(c=C.placement),C.rects&&(u=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:f,y:d}=Nf(u,c,l)),p=-1)}return{x:f,y:d,placement:c,strategy:o,middlewareData:y}};async function ri(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:c=!1,padding:y=0}=Gt(t,e),x=Sv(y),S=a[c?d==="floating"?"reference":"floating":d],m=Fs(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:l})),v=d==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,w=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),E=await(i.isElement==null?void 0:i.isElement(w))?await(i.getScale==null?void 0:i.getScale(w))||{x:1,y:1}:{x:1,y:1},C=Fs(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:l}):v);return{top:(m.top-C.top+x.top)/E.y,bottom:(C.bottom-m.bottom+x.bottom)/E.y,left:(m.left-C.left+x.left)/E.x,right:(C.right-m.right+x.right)/E.x}}const CS=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:f=0}=Gt(e,t)||{};if(u==null)return{};const d=Sv(f),c={x:n,y:r},y=jc(o),x=Ac(y),p=await s.getDimensions(u),S=y==="y",m=S?"top":"left",v=S?"bottom":"right",w=S?"clientHeight":"clientWidth",E=i.reference[x]+i.reference[y]-c[y]-i.floating[x],C=c[y]-i.reference[y],k=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let N=k?k[w]:0;(!N||!await(s.isElement==null?void 0:s.isElement(k)))&&(N=a.floating[w]||i.floating[x]);const P=E/2-C/2,M=N/2-p[x]/2-1,A=jn(d[m],M),$=jn(d[v],M),L=A,B=N-p[x]-$,j=N/2-p[x]/2+P,Q=yu(L,j,B),z=!l.arrow&&uo(o)!=null&&j!==Q&&i.reference[x]/2-(j<L?A:$)-p[x]/2<0,K=z?j<L?j-L:j-B:0;return{[y]:c[y]+K,data:{[y]:Q,centerOffset:j-Q-K,...z&&{alignmentOffset:K}},reset:z}}}),bS=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:c,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:p=!0,...S}=Gt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const m=Yt(o),v=In(a),w=Yt(a)===a,E=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=c||(w||!p?[Ls(a)]:yS(a)),k=x!=="none";!c&&k&&C.push(...xS(a,p,x,E));const N=[a,...C],P=await ri(t,S),M=[];let A=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&M.push(P[m]),d){const j=gS(o,s,E);M.push(P[j[0]],P[j[1]])}if(A=[...A,{placement:o,overflows:M}],!M.every(j=>j<=0)){var $,L;const j=((($=i.flip)==null?void 0:$.index)||0)+1,Q=N[j];if(Q)return{data:{index:j,overflows:A},reset:{placement:Q}};let z=(L=A.filter(K=>K.overflows[0]<=0).sort((K,b)=>K.overflows[1]-b.overflows[1])[0])==null?void 0:L.placement;if(!z)switch(y){case"bestFit":{var B;const K=(B=A.filter(b=>{if(k){const _=In(b.placement);return _===v||_==="y"}return!0}).map(b=>[b.placement,b.overflows.filter(_=>_>0).reduce((_,F)=>_+F,0)]).sort((b,_)=>b[1]-_[1])[0])==null?void 0:B[0];K&&(z=K);break}case"initialPlacement":z=a;break}if(o!==z)return{reset:{placement:z}}}return{}}}};function Tf(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Rf(e){return hS.some(t=>e[t]>=0)}const kS=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Gt(e,t);switch(r){case"referenceHidden":{const i=await ri(t,{...o,elementContext:"reference"}),s=Tf(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Rf(s)}}}case"escaped":{const i=await ri(t,{...o,altBoundary:!0}),s=Tf(i,n.floating);return{data:{escapedOffsets:s,escaped:Rf(s)}}}default:return{}}}}};async function PS(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Yt(n),a=uo(n),l=In(n)==="y",u=["left","top"].includes(s)?-1:1,f=i&&l?-1:1,d=Gt(t,e);let{mainAxis:c,crossAxis:y,alignmentAxis:x}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof x=="number"&&(y=a==="end"?x*-1:x),l?{x:y*f,y:c*u}:{x:c*u,y:y*f}}const NS=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,l=await PS(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},TS=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:S=>{let{x:m,y:v}=S;return{x:m,y:v}}},...l}=Gt(e,t),u={x:n,y:r},f=await ri(t,l),d=In(Yt(o)),c=Oc(d);let y=u[c],x=u[d];if(i){const S=c==="y"?"top":"left",m=c==="y"?"bottom":"right",v=y+f[S],w=y-f[m];y=yu(v,y,w)}if(s){const S=d==="y"?"top":"left",m=d==="y"?"bottom":"right",v=x+f[S],w=x-f[m];x=yu(v,x,w)}const p=a.fn({...t,[c]:y,[d]:x});return{...p,data:{x:p.x-n,y:p.y-r,enabled:{[c]:i,[d]:s}}}}}},RS=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=Gt(e,t),f={x:n,y:r},d=In(o),c=Oc(d);let y=f[c],x=f[d];const p=Gt(a,t),S=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const w=c==="y"?"height":"width",E=i.reference[c]-i.floating[w]+S.mainAxis,C=i.reference[c]+i.reference[w]-S.mainAxis;y<E?y=E:y>C&&(y=C)}if(u){var m,v;const w=c==="y"?"width":"height",E=["top","left"].includes(Yt(o)),C=i.reference[d]-i.floating[w]+(E&&((m=s.offset)==null?void 0:m[d])||0)+(E?0:S.crossAxis),k=i.reference[d]+i.reference[w]+(E?0:((v=s.offset)==null?void 0:v[d])||0)-(E?S.crossAxis:0);x<C?x=C:x>k&&(x=k)}return{[c]:y,[d]:x}}}},_S=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:a}=t,{apply:l=()=>{},...u}=Gt(e,t),f=await ri(t,u),d=Yt(o),c=uo(o),y=In(o)==="y",{width:x,height:p}=i.floating;let S,m;d==="top"||d==="bottom"?(S=d,m=c===(await(s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(m=d,S=c==="end"?"top":"bottom");const v=p-f.top-f.bottom,w=x-f.left-f.right,E=jn(p-f[S],v),C=jn(x-f[m],w),k=!t.middlewareData.shift;let N=E,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=v),k&&!c){const A=Ye(f.left,0),$=Ye(f.right,0),L=Ye(f.top,0),B=Ye(f.bottom,0);y?P=x-2*(A!==0||$!==0?A+$:Ye(f.left,f.right)):N=p-2*(L!==0||B!==0?L+B:Ye(f.top,f.bottom))}await l({...t,availableWidth:P,availableHeight:N});const M=await s.getDimensions(a.floating);return x!==M.width||p!==M.height?{reset:{rects:!0}}:{}}}};function ha(){return typeof window<"u"}function co(e){return Ev(e)?(e.nodeName||"").toLowerCase():"#document"}function Ze(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function It(e){var t;return(t=(Ev(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ev(e){return ha()?e instanceof Node||e instanceof Ze(e).Node:!1}function xt(e){return ha()?e instanceof Element||e instanceof Ze(e).Element:!1}function Mt(e){return ha()?e instanceof HTMLElement||e instanceof Ze(e).HTMLElement:!1}function _f(e){return!ha()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ze(e).ShadowRoot}function mi(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=St(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function OS(e){return["table","td","th"].includes(co(e))}function ma(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Mc(e){const t=Ic(),n=xt(e)?St(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function AS(e){let t=Dn(e);for(;Mt(t)&&!ro(t);){if(Mc(t))return t;if(ma(t))return null;t=Dn(t)}return null}function Ic(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ro(e){return["html","body","#document"].includes(co(e))}function St(e){return Ze(e).getComputedStyle(e)}function va(e){return xt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Dn(e){if(co(e)==="html")return e;const t=e.assignedSlot||e.parentNode||_f(e)&&e.host||It(e);return _f(t)?t.host:t}function Cv(e){const t=Dn(e);return ro(t)?e.ownerDocument?e.ownerDocument.body:e.body:Mt(t)&&mi(t)?t:Cv(t)}function oi(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Cv(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Ze(o);if(i){const a=xu(s);return t.concat(s,s.visualViewport||[],mi(o)?o:[],a&&n?oi(a):[])}return t.concat(o,oi(o,[],n))}function xu(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function bv(e){const t=St(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Mt(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=Ds(n)!==i||Ds(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function Dc(e){return xt(e)?e:e.contextElement}function Lr(e){const t=Dc(e);if(!Mt(t))return Mn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=bv(t);let s=(i?Ds(n.width):n.width)/r,a=(i?Ds(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const jS=Mn(0);function kv(e){const t=Ze(e);return!Ic()||!t.visualViewport?jS:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function MS(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ze(e)?!1:t}function rr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Dc(e);let s=Mn(1);t&&(r?xt(r)&&(s=Lr(r)):s=Lr(e));const a=MS(i,n,r)?kv(i):Mn(0);let l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,f=o.width/s.x,d=o.height/s.y;if(i){const c=Ze(i),y=r&&xt(r)?Ze(r):r;let x=c,p=xu(x);for(;p&&r&&y!==x;){const S=Lr(p),m=p.getBoundingClientRect(),v=St(p),w=m.left+(p.clientLeft+parseFloat(v.paddingLeft))*S.x,E=m.top+(p.clientTop+parseFloat(v.paddingTop))*S.y;l*=S.x,u*=S.y,f*=S.x,d*=S.y,l+=w,u+=E,x=Ze(p),p=xu(x)}}return Fs({width:f,height:d,x:l,y:u})}function IS(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=It(r),a=t?ma(t.floating):!1;if(r===s||a&&i)return n;let l={scrollLeft:0,scrollTop:0},u=Mn(1);const f=Mn(0),d=Mt(r);if((d||!d&&!i)&&((co(r)!=="body"||mi(s))&&(l=va(r)),Mt(r))){const c=rr(r);u=Lr(r),f.x=c.x+r.clientLeft,f.y=c.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+f.x,y:n.y*u.y-l.scrollTop*u.y+f.y}}function DS(e){return Array.from(e.getClientRects())}function Su(e,t){const n=va(e).scrollLeft;return t?t.left+n:rr(It(e)).left+n}function LS(e){const t=It(e),n=va(e),r=e.ownerDocument.body,o=Ye(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Ye(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Su(e);const a=-n.scrollTop;return St(r).direction==="rtl"&&(s+=Ye(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function FS(e,t){const n=Ze(e),r=It(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;const u=Ic();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}function zS(e,t){const n=rr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Mt(e)?Lr(e):Mn(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=o*i.x,u=r*i.y;return{width:s,height:a,x:l,y:u}}function Of(e,t,n){let r;if(t==="viewport")r=FS(e,n);else if(t==="document")r=LS(It(e));else if(xt(t))r=zS(t,n);else{const o=kv(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Fs(r)}function Pv(e,t){const n=Dn(e);return n===t||!xt(n)||ro(n)?!1:St(n).position==="fixed"||Pv(n,t)}function $S(e,t){const n=t.get(e);if(n)return n;let r=oi(e,[],!1).filter(a=>xt(a)&&co(a)!=="body"),o=null;const i=St(e).position==="fixed";let s=i?Dn(e):e;for(;xt(s)&&!ro(s);){const a=St(s),l=Mc(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||mi(s)&&!l&&Pv(e,s))?r=r.filter(f=>f!==s):o=a,s=Dn(s)}return t.set(e,r),r}function US(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?ma(t)?[]:$S(t,this._c):[].concat(n),r],a=s[0],l=s.reduce((u,f)=>{const d=Of(t,f,o);return u.top=Ye(d.top,u.top),u.right=jn(d.right,u.right),u.bottom=jn(d.bottom,u.bottom),u.left=Ye(d.left,u.left),u},Of(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function BS(e){const{width:t,height:n}=bv(e);return{width:t,height:n}}function VS(e,t,n){const r=Mt(t),o=It(t),i=n==="fixed",s=rr(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=Mn(0);if(r||!r&&!i)if((co(t)!=="body"||mi(o))&&(a=va(t)),r){const y=rr(t,!0,i,t);l.x=y.x+t.clientLeft,l.y=y.y+t.clientTop}else o&&(l.x=Su(o));let u=0,f=0;if(o&&!r&&!i){const y=o.getBoundingClientRect();f=y.top+a.scrollTop,u=y.left+a.scrollLeft-Su(o,y)}const d=s.left+a.scrollLeft-l.x-u,c=s.top+a.scrollTop-l.y-f;return{x:d,y:c,width:s.width,height:s.height}}function al(e){return St(e).position==="static"}function Af(e,t){if(!Mt(e)||St(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return It(e)===n&&(n=n.ownerDocument.body),n}function Nv(e,t){const n=Ze(e);if(ma(e))return n;if(!Mt(e)){let o=Dn(e);for(;o&&!ro(o);){if(xt(o)&&!al(o))return o;o=Dn(o)}return n}let r=Af(e,t);for(;r&&OS(r)&&al(r);)r=Af(r,t);return r&&ro(r)&&al(r)&&!Mc(r)?n:r||AS(e)||n}const WS=async function(e){const t=this.getOffsetParent||Nv,n=this.getDimensions,r=await n(e.floating);return{reference:VS(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function HS(e){return St(e).direction==="rtl"}const QS={convertOffsetParentRelativeRectToViewportRelativeRect:IS,getDocumentElement:It,getClippingRect:US,getOffsetParent:Nv,getElementRects:WS,getClientRects:DS,getDimensions:BS,getScale:Lr,isElement:xt,isRTL:HS};function KS(e,t){let n=null,r;const o=It(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();const{left:u,top:f,width:d,height:c}=e.getBoundingClientRect();if(a||t(),!d||!c)return;const y=Ui(f),x=Ui(o.clientWidth-(u+d)),p=Ui(o.clientHeight-(f+c)),S=Ui(u),v={rootMargin:-y+"px "+-x+"px "+-p+"px "+-S+"px",threshold:Ye(0,jn(1,l))||1};let w=!0;function E(C){const k=C[0].intersectionRatio;if(k!==l){if(!w)return s();k?s(!1,k):r=setTimeout(()=>{s(!1,1e-7)},1e3)}w=!1}try{n=new IntersectionObserver(E,{...v,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,v)}n.observe(e)}return s(!0),i}function GS(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Dc(e),f=o||i?[...u?oi(u):[],...oi(t)]:[];f.forEach(m=>{o&&m.addEventListener("scroll",n,{passive:!0}),i&&m.addEventListener("resize",n)});const d=u&&a?KS(u,n):null;let c=-1,y=null;s&&(y=new ResizeObserver(m=>{let[v]=m;v&&v.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(c),c=requestAnimationFrame(()=>{var w;(w=y)==null||w.observe(t)})),n()}),u&&!l&&y.observe(u),y.observe(t));let x,p=l?rr(e):null;l&&S();function S(){const m=rr(e);p&&(m.x!==p.x||m.y!==p.y||m.width!==p.width||m.height!==p.height)&&n(),p=m,x=requestAnimationFrame(S)}return n(),()=>{var m;f.forEach(v=>{o&&v.removeEventListener("scroll",n),i&&v.removeEventListener("resize",n)}),d==null||d(),(m=y)==null||m.disconnect(),y=null,l&&cancelAnimationFrame(x)}}const YS=NS,XS=TS,qS=bS,ZS=_S,JS=kS,jf=CS,eE=RS,tE=(e,t,n)=>{const r=new Map,o={platform:QS,...n},i={...o.platform,_c:r};return ES(e,t,{...o,platform:i})};var as=typeof document<"u"?h.useLayoutEffect:h.useEffect;function zs(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!zs(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!zs(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Tv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Mf(e,t){const n=Tv(e);return Math.round(t*n)/n}function ll(e){const t=h.useRef(e);return as(()=>{t.current=e}),t}function nE(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[f,d]=h.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[c,y]=h.useState(r);zs(c,r)||y(r);const[x,p]=h.useState(null),[S,m]=h.useState(null),v=h.useCallback(b=>{b!==k.current&&(k.current=b,p(b))},[]),w=h.useCallback(b=>{b!==N.current&&(N.current=b,m(b))},[]),E=i||x,C=s||S,k=h.useRef(null),N=h.useRef(null),P=h.useRef(f),M=l!=null,A=ll(l),$=ll(o),L=ll(u),B=h.useCallback(()=>{if(!k.current||!N.current)return;const b={placement:t,strategy:n,middleware:c};$.current&&(b.platform=$.current),tE(k.current,N.current,b).then(_=>{const F={..._,isPositioned:L.current!==!1};j.current&&!zs(P.current,F)&&(P.current=F,hi.flushSync(()=>{d(F)}))})},[c,t,n,$,L]);as(()=>{u===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,d(b=>({...b,isPositioned:!1})))},[u]);const j=h.useRef(!1);as(()=>(j.current=!0,()=>{j.current=!1}),[]),as(()=>{if(E&&(k.current=E),C&&(N.current=C),E&&C){if(A.current)return A.current(E,C,B);B()}},[E,C,B,A,M]);const Q=h.useMemo(()=>({reference:k,floating:N,setReference:v,setFloating:w}),[v,w]),z=h.useMemo(()=>({reference:E,floating:C}),[E,C]),K=h.useMemo(()=>{const b={position:n,left:0,top:0};if(!z.floating)return b;const _=Mf(z.floating,f.x),F=Mf(z.floating,f.y);return a?{...b,transform:"translate("+_+"px, "+F+"px)",...Tv(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:_,top:F}},[n,a,z.floating,f.x,f.y]);return h.useMemo(()=>({...f,update:B,refs:Q,elements:z,floatingStyles:K}),[f,B,Q,z,K])}const rE=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?jf({element:r.current,padding:o}).fn(n):{}:r?jf({element:r,padding:o}).fn(n):{}}}},oE=(e,t)=>({...YS(e),options:[e,t]}),iE=(e,t)=>({...XS(e),options:[e,t]}),sE=(e,t)=>({...eE(e),options:[e,t]}),aE=(e,t)=>({...qS(e),options:[e,t]}),lE=(e,t)=>({...ZS(e),options:[e,t]}),uE=(e,t)=>({...JS(e),options:[e,t]}),cE=(e,t)=>({...rE(e),options:[e,t]});var dE="Arrow",Rv=h.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return g.jsx(te.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:g.jsx("polygon",{points:"0,0 30,0 15,10"})})});Rv.displayName=dE;var fE=Rv;function pE(e,t=[]){let n=[];function r(i,s){const a=h.createContext(s),l=n.length;n=[...n,s];function u(d){const{scope:c,children:y,...x}=d,p=(c==null?void 0:c[e][l])||a,S=h.useMemo(()=>x,Object.values(x));return g.jsx(p.Provider,{value:S,children:y})}function f(d,c){const y=(c==null?void 0:c[e][l])||a,x=h.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>h.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,hE(o,...t)]}function hE(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function mE(e){const[t,n]=h.useState(void 0);return An(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,a;if("borderBoxSize"in i){const l=i.borderBoxSize,u=Array.isArray(l)?l[0]:l;s=u.inlineSize,a=u.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var _v="Popper",[Ov,Av]=pE(_v),[Gk,jv]=Ov(_v),Mv="PopperAnchor",Iv=h.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=jv(Mv,n),s=h.useRef(null),a=Ae(t,s);return h.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:g.jsx(te.div,{...o,ref:a})});Iv.displayName=Mv;var Lc="PopperContent",[vE,gE]=Ov(Lc),Dv=h.forwardRef((e,t)=>{var ct,Zt,Jt,en,vi,lr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:c=!1,updatePositionStrategy:y="optimized",onPlaced:x,...p}=e,S=jv(Lc,n),[m,v]=h.useState(null),w=Ae(t,$n=>v($n)),[E,C]=h.useState(null),k=mE(E),N=(k==null?void 0:k.width)??0,P=(k==null?void 0:k.height)??0,M=r+(i!=="center"?"-"+i:""),A=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},$=Array.isArray(u)?u:[u],L=$.length>0,B={padding:A,boundary:$.filter(wE),altBoundary:L},{refs:j,floatingStyles:Q,placement:z,isPositioned:K,middlewareData:b}=nE({strategy:"fixed",placement:M,whileElementsMounted:(...$n)=>GS(...$n,{animationFrame:y==="always"}),elements:{reference:S.anchor},middleware:[oE({mainAxis:o+P,alignmentAxis:s}),l&&iE({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?sE():void 0,...B}),l&&aE({...B}),lE({...B,apply:({elements:$n,rects:fo,availableWidth:gi,availableHeight:tn})=>{const{width:Pa,height:Na}=fo.reference,Ee=$n.floating.style;Ee.setProperty("--radix-popper-available-width",`${gi}px`),Ee.setProperty("--radix-popper-available-height",`${tn}px`),Ee.setProperty("--radix-popper-anchor-width",`${Pa}px`),Ee.setProperty("--radix-popper-anchor-height",`${Na}px`)}}),E&&cE({element:E,padding:a}),xE({arrowWidth:N,arrowHeight:P}),c&&uE({strategy:"referenceHidden",...B})]}),[_,F]=zv(z),D=et(x);An(()=>{K&&(D==null||D())},[K,D]);const U=(ct=b.arrow)==null?void 0:ct.x,Y=(Zt=b.arrow)==null?void 0:Zt.y,ce=((Jt=b.arrow)==null?void 0:Jt.centerOffset)!==0,[He,J]=h.useState();return An(()=>{m&&J(window.getComputedStyle(m).zIndex)},[m]),g.jsx("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:K?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:He,"--radix-popper-transform-origin":[(en=b.transformOrigin)==null?void 0:en.x,(vi=b.transformOrigin)==null?void 0:vi.y].join(" "),...((lr=b.hide)==null?void 0:lr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:g.jsx(vE,{scope:n,placedSide:_,onArrowChange:C,arrowX:U,arrowY:Y,shouldHideArrow:ce,children:g.jsx(te.div,{"data-side":_,"data-align":F,...p,ref:w,style:{...p.style,animation:K?void 0:"none"}})})})});Dv.displayName=Lc;var Lv="PopperArrow",yE={top:"bottom",right:"left",bottom:"top",left:"right"},Fv=h.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=gE(Lv,r),s=yE[i.placedSide];return g.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:g.jsx(fE,{...o,ref:n,style:{...o.style,display:"block"}})})});Fv.displayName=Lv;function wE(e){return e!==null}var xE=e=>({name:"transformOrigin",options:e,fn(t){var S,m,v;const{placement:n,rects:r,middlewareData:o}=t,s=((S=o.arrow)==null?void 0:S.centerOffset)!==0,a=s?0:e.arrowWidth,l=s?0:e.arrowHeight,[u,f]=zv(n),d={start:"0%",center:"50%",end:"100%"}[f],c=(((m=o.arrow)==null?void 0:m.x)??0)+a/2,y=(((v=o.arrow)==null?void 0:v.y)??0)+l/2;let x="",p="";return u==="bottom"?(x=s?d:`${c}px`,p=`${-l}px`):u==="top"?(x=s?d:`${c}px`,p=`${r.floating.height+l}px`):u==="right"?(x=`${-l}px`,p=s?d:`${y}px`):u==="left"&&(x=`${r.floating.width+l}px`,p=s?d:`${y}px`),{data:{x,y:p}}}});function zv(e){const[t,n="center"]=e.split("-");return[t,n]}var SE=Iv,EE=Dv,CE=Fv,[ga,Yk]=aa("Tooltip",[Av]),Fc=Av(),$v="TooltipProvider",bE=700,If="tooltip.open",[kE,Uv]=ga($v),Bv=e=>{const{__scopeTooltip:t,delayDuration:n=bE,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,a]=h.useState(!0),l=h.useRef(!1),u=h.useRef(0);return h.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),g.jsx(kE,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:h.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:h.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:h.useCallback(f=>{l.current=f},[]),disableHoverableContent:o,children:i})};Bv.displayName=$v;var Vv="Tooltip",[Xk,ya]=ga(Vv),Eu="TooltipTrigger",PE=h.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=ya(Eu,n),i=Uv(Eu,n),s=Fc(n),a=h.useRef(null),l=Ae(t,a,o.onTriggerChange),u=h.useRef(!1),f=h.useRef(!1),d=h.useCallback(()=>u.current=!1,[]);return h.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),g.jsx(SE,{asChild:!0,...s,children:g.jsx(te.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:X(e.onPointerMove,c=>{c.pointerType!=="touch"&&!f.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:X(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:X(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:X(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:X(e.onBlur,o.onClose),onClick:X(e.onClick,o.onClose)})})});PE.displayName=Eu;var NE="TooltipPortal",[qk,TE]=ga(NE,{forceMount:void 0}),oo="TooltipContent",Wv=h.forwardRef((e,t)=>{const n=TE(oo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=ya(oo,e.__scopeTooltip);return g.jsx(ar,{present:r||s.open,children:s.disableHoverableContent?g.jsx(Hv,{side:o,...i,ref:t}):g.jsx(RE,{side:o,...i,ref:t})})}),RE=h.forwardRef((e,t)=>{const n=ya(oo,e.__scopeTooltip),r=Uv(oo,e.__scopeTooltip),o=h.useRef(null),i=Ae(t,o),[s,a]=h.useState(null),{trigger:l,onClose:u}=n,f=o.current,{onPointerInTransitChange:d}=r,c=h.useCallback(()=>{a(null),d(!1)},[d]),y=h.useCallback((x,p)=>{const S=x.currentTarget,m={x:x.clientX,y:x.clientY},v=jE(m,S.getBoundingClientRect()),w=ME(m,v),E=IE(p.getBoundingClientRect()),C=LE([...w,...E]);a(C),d(!0)},[d]);return h.useEffect(()=>()=>c(),[c]),h.useEffect(()=>{if(l&&f){const x=S=>y(S,f),p=S=>y(S,l);return l.addEventListener("pointerleave",x),f.addEventListener("pointerleave",p),()=>{l.removeEventListener("pointerleave",x),f.removeEventListener("pointerleave",p)}}},[l,f,y,c]),h.useEffect(()=>{if(s){const x=p=>{const S=p.target,m={x:p.clientX,y:p.clientY},v=(l==null?void 0:l.contains(S))||(f==null?void 0:f.contains(S)),w=!DE(m,s);v?c():w&&(c(),u())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[l,f,s,u,c]),g.jsx(Hv,{...e,ref:i})}),[_E,OE]=ga(Vv,{isInside:!1}),Hv=h.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...a}=e,l=ya(oo,n),u=Fc(n),{onClose:f}=l;return h.useEffect(()=>(document.addEventListener(If,f),()=>document.removeEventListener(If,f)),[f]),h.useEffect(()=>{if(l.trigger){const d=c=>{const y=c.target;y!=null&&y.contains(l.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[l.trigger,f]),g.jsx(la,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:g.jsxs(EE,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[g.jsx(Mm,{children:r}),g.jsx(_E,{scope:n,isInside:!0,children:g.jsx(Rx,{id:l.contentId,role:"tooltip",children:o||r})})]})})});Wv.displayName=oo;var Qv="TooltipArrow",AE=h.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Fc(n);return OE(Qv,n).isInside?null:g.jsx(CE,{...o,...r,ref:t})});AE.displayName=Qv;function jE(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function ME(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function IE(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function DE(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const a=t[i].x,l=t[i].y,u=t[s].x,f=t[s].y;l>r!=f>r&&n<(u-a)*(r-l)/(f-l)+a&&(o=!o)}return o}function LE(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),FE(t)}function FE(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var zE=Bv,Kv=Wv;const $E=zE,UE=h.forwardRef(({className:e,sideOffset:t=4,...n},r)=>g.jsx(Kv,{ref:r,sideOffset:t,className:ne("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));UE.displayName=Kv.displayName;var wa=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},xa=typeof window>"u"||"Deno"in globalThis;function pt(){}function BE(e,t){return typeof e=="function"?e(t):e}function VE(e){return typeof e=="number"&&e>=0&&e!==1/0}function WE(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Df(e,t){return typeof e=="function"?e(t):e}function HE(e,t){return typeof e=="function"?e(t):e}function Lf(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:a}=e;if(s){if(r){if(t.queryHash!==zc(s,t.options))return!1}else if(!si(t.queryKey,s))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||i&&!i(t))}function Ff(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(ii(t.options.mutationKey)!==ii(i))return!1}else if(!si(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function zc(e,t){return((t==null?void 0:t.queryKeyHashFn)||ii)(e)}function ii(e){return JSON.stringify(e,(t,n)=>Cu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function si(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!si(e[n],t[n])):!1}function Gv(e,t){if(e===t)return e;const n=zf(e)&&zf(t);if(n||Cu(e)&&Cu(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,a=n?[]:{};let l=0;for(let u=0;u<s;u++){const f=n?u:i[u];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(a[f]=void 0,l++):(a[f]=Gv(e[f],t[f]),a[f]===e[f]&&e[f]!==void 0&&l++)}return o===s&&l===o?e:a}return t}function zf(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Cu(e){if(!$f(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!$f(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function $f(e){return Object.prototype.toString.call(e)==="[object Object]"}function QE(e){return new Promise(t=>{setTimeout(t,e)})}function KE(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Gv(e,t):t}function GE(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function YE(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var $c=Symbol();function Yv(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===$c?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Qn,hn,$r,up,XE=(up=class extends wa{constructor(){super();Z(this,Qn);Z(this,hn);Z(this,$r);H(this,$r,t=>{if(!xa&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){T(this,hn)||this.setEventListener(T(this,$r))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,hn))==null||t.call(this),H(this,hn,void 0))}setEventListener(t){var n;H(this,$r,t),(n=T(this,hn))==null||n.call(this),H(this,hn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){T(this,Qn)!==t&&(H(this,Qn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof T(this,Qn)=="boolean"?T(this,Qn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Qn=new WeakMap,hn=new WeakMap,$r=new WeakMap,up),Xv=new XE,Ur,mn,Br,cp,qE=(cp=class extends wa{constructor(){super();Z(this,Ur,!0);Z(this,mn);Z(this,Br);H(this,Br,t=>{if(!xa&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){T(this,mn)||this.setEventListener(T(this,Br))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,mn))==null||t.call(this),H(this,mn,void 0))}setEventListener(t){var n;H(this,Br,t),(n=T(this,mn))==null||n.call(this),H(this,mn,t(this.setOnline.bind(this)))}setOnline(t){T(this,Ur)!==t&&(H(this,Ur,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return T(this,Ur)}},Ur=new WeakMap,mn=new WeakMap,Br=new WeakMap,cp),$s=new qE;function ZE(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function JE(e){return Math.min(1e3*2**e,3e4)}function qv(e){return(e??"online")==="online"?$s.isOnline():!0}var Zv=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function ul(e){return e instanceof Zv}function Jv(e){let t=!1,n=0,r=!1,o;const i=ZE(),s=p=>{var S;r||(c(new Zv(p)),(S=e.abort)==null||S.call(e))},a=()=>{t=!0},l=()=>{t=!1},u=()=>Xv.isFocused()&&(e.networkMode==="always"||$s.isOnline())&&e.canRun(),f=()=>qv(e.networkMode)&&e.canRun(),d=p=>{var S;r||(r=!0,(S=e.onSuccess)==null||S.call(e,p),o==null||o(),i.resolve(p))},c=p=>{var S;r||(r=!0,(S=e.onError)==null||S.call(e,p),o==null||o(),i.reject(p))},y=()=>new Promise(p=>{var S;o=m=>{(r||u())&&p(m)},(S=e.onPause)==null||S.call(e)}).then(()=>{var p;o=void 0,r||(p=e.onContinue)==null||p.call(e)}),x=()=>{if(r)return;let p;const S=n===0?e.initialPromise:void 0;try{p=S??e.fn()}catch(m){p=Promise.reject(m)}Promise.resolve(p).then(d).catch(m=>{var k;if(r)return;const v=e.retry??(xa?0:3),w=e.retryDelay??JE,E=typeof w=="function"?w(n,m):w,C=v===!0||typeof v=="number"&&n<v||typeof v=="function"&&v(n,m);if(t||!C){c(m);return}n++,(k=e.onFail)==null||k.call(e,n,m),QE(E).then(()=>u()?void 0:y()).then(()=>{t?c(m):x()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:a,continueRetry:l,canStart:f,start:()=>(f()?x():y().then(x),i)}}function eC(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const i=a=>{t?e.push(a):o(()=>{n(a)})},s=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||s()}return l},batchCalls:a=>(...l)=>{i(()=>{a(...l)})},schedule:i,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var Ie=eC(),Kn,dp,eg=(dp=class{constructor(){Z(this,Kn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),VE(this.gcTime)&&H(this,Kn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(xa?1/0:5*60*1e3))}clearGcTimeout(){T(this,Kn)&&(clearTimeout(T(this,Kn)),H(this,Kn,void 0))}},Kn=new WeakMap,dp),Vr,Wr,rt,Re,ai,Gn,ht,Lt,fp,tC=(fp=class extends eg{constructor(t){super();Z(this,ht);Z(this,Vr);Z(this,Wr);Z(this,rt);Z(this,Re);Z(this,ai);Z(this,Gn);H(this,Gn,!1),H(this,ai,t.defaultOptions),this.setOptions(t.options),this.observers=[],H(this,rt,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,H(this,Vr,rC(this.options)),this.state=t.state??T(this,Vr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=T(this,Re))==null?void 0:t.promise}setOptions(t){this.options={...T(this,ai),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&T(this,rt).remove(this)}setData(t,n){const r=KE(this.state.data,t,this.options);return Pe(this,ht,Lt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Pe(this,ht,Lt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=T(this,Re))==null?void 0:r.promise;return(o=T(this,Re))==null||o.cancel(t),n?n.then(pt).catch(pt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(T(this,Vr))}isActive(){return this.observers.some(t=>HE(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===$c||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!WE(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,Re))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,Re))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),T(this,rt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(T(this,Re)&&(T(this,Gn)?T(this,Re).cancel({revert:!0}):T(this,Re).cancelRetry()),this.scheduleGc()),T(this,rt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Pe(this,ht,Lt).call(this,{type:"invalidate"})}fetch(t,n){var l,u,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(T(this,Re))return T(this,Re).continueRetry(),T(this,Re).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(c=>c.options.queryFn);d&&this.setOptions(d.options)}const r=new AbortController,o=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(H(this,Gn,!0),r.signal)})},i=()=>{const d=Yv(this.options,n),c={queryKey:this.queryKey,meta:this.meta};return o(c),H(this,Gn,!1),this.options.persister?this.options.persister(d,c,this):d(c)},s={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};o(s),(l=this.options.behavior)==null||l.onFetch(s,this),H(this,Wr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=s.fetchOptions)==null?void 0:u.meta))&&Pe(this,ht,Lt).call(this,{type:"fetch",meta:(f=s.fetchOptions)==null?void 0:f.meta});const a=d=>{var c,y,x,p;ul(d)&&d.silent||Pe(this,ht,Lt).call(this,{type:"error",error:d}),ul(d)||((y=(c=T(this,rt).config).onError)==null||y.call(c,d,this),(p=(x=T(this,rt).config).onSettled)==null||p.call(x,this.state.data,d,this)),this.scheduleGc()};return H(this,Re,Jv({initialPromise:n==null?void 0:n.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var c,y,x,p;if(d===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(S){a(S);return}(y=(c=T(this,rt).config).onSuccess)==null||y.call(c,d,this),(p=(x=T(this,rt).config).onSettled)==null||p.call(x,d,this.state.error,this),this.scheduleGc()},onError:a,onFail:(d,c)=>{Pe(this,ht,Lt).call(this,{type:"failed",failureCount:d,error:c})},onPause:()=>{Pe(this,ht,Lt).call(this,{type:"pause"})},onContinue:()=>{Pe(this,ht,Lt).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),T(this,Re).start()}},Vr=new WeakMap,Wr=new WeakMap,rt=new WeakMap,Re=new WeakMap,ai=new WeakMap,Gn=new WeakMap,ht=new WeakSet,Lt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...nC(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return ul(o)&&o.revert&&T(this,Wr)?{...T(this,Wr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Ie.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),T(this,rt).notify({query:this,type:"updated",action:t})})},fp);function nC(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:qv(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function rC(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Nt,pp,oC=(pp=class extends wa{constructor(t={}){super();Z(this,Nt);this.config=t,H(this,Nt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??zc(o,n);let s=this.get(i);return s||(s=new tC({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){T(this,Nt).has(t.queryHash)||(T(this,Nt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=T(this,Nt).get(t.queryHash);n&&(t.destroy(),n===t&&T(this,Nt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ie.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return T(this,Nt).get(t)}getAll(){return[...T(this,Nt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Lf(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Lf(t,r)):n}notify(t){Ie.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Ie.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ie.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Nt=new WeakMap,pp),Tt,je,Yn,Rt,an,hp,iC=(hp=class extends eg{constructor(t){super();Z(this,Rt);Z(this,Tt);Z(this,je);Z(this,Yn);this.mutationId=t.mutationId,H(this,je,t.mutationCache),H(this,Tt,[]),this.state=t.state||sC(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){T(this,Tt).includes(t)||(T(this,Tt).push(t),this.clearGcTimeout(),T(this,je).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){H(this,Tt,T(this,Tt).filter(n=>n!==t)),this.scheduleGc(),T(this,je).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){T(this,Tt).length||(this.state.status==="pending"?this.scheduleGc():T(this,je).remove(this))}continue(){var t;return((t=T(this,Yn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,a,l,u,f,d,c,y,x,p,S,m,v,w,E,C,k,N;H(this,Yn,Jv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,M)=>{Pe(this,Rt,an).call(this,{type:"failed",failureCount:P,error:M})},onPause:()=>{Pe(this,Rt,an).call(this,{type:"pause"})},onContinue:()=>{Pe(this,Rt,an).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>T(this,je).canRun(this)}));const n=this.state.status==="pending",r=!T(this,Yn).canStart();try{if(!n){Pe(this,Rt,an).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(o=T(this,je).config).onMutate)==null?void 0:i.call(o,t,this));const M=await((a=(s=this.options).onMutate)==null?void 0:a.call(s,t));M!==this.state.context&&Pe(this,Rt,an).call(this,{type:"pending",context:M,variables:t,isPaused:r})}const P=await T(this,Yn).start();return await((u=(l=T(this,je).config).onSuccess)==null?void 0:u.call(l,P,t,this.state.context,this)),await((d=(f=this.options).onSuccess)==null?void 0:d.call(f,P,t,this.state.context)),await((y=(c=T(this,je).config).onSettled)==null?void 0:y.call(c,P,null,this.state.variables,this.state.context,this)),await((p=(x=this.options).onSettled)==null?void 0:p.call(x,P,null,t,this.state.context)),Pe(this,Rt,an).call(this,{type:"success",data:P}),P}catch(P){try{throw await((m=(S=T(this,je).config).onError)==null?void 0:m.call(S,P,t,this.state.context,this)),await((w=(v=this.options).onError)==null?void 0:w.call(v,P,t,this.state.context)),await((C=(E=T(this,je).config).onSettled)==null?void 0:C.call(E,void 0,P,this.state.variables,this.state.context,this)),await((N=(k=this.options).onSettled)==null?void 0:N.call(k,void 0,P,t,this.state.context)),P}finally{Pe(this,Rt,an).call(this,{type:"error",error:P})}}finally{T(this,je).runNext(this)}}},Tt=new WeakMap,je=new WeakMap,Yn=new WeakMap,Rt=new WeakSet,an=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Ie.batch(()=>{T(this,Tt).forEach(r=>{r.onMutationUpdate(t)}),T(this,je).notify({mutation:this,type:"updated",action:t})})},hp);function sC(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Qe,li,mp,aC=(mp=class extends wa{constructor(t={}){super();Z(this,Qe);Z(this,li);this.config=t,H(this,Qe,new Map),H(this,li,Date.now())}build(t,n,r){const o=new iC({mutationCache:this,mutationId:++xi(this,li)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=Bi(t),r=T(this,Qe).get(n)??[];r.push(t),T(this,Qe).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Bi(t);if(T(this,Qe).has(n)){const o=(r=T(this,Qe).get(n))==null?void 0:r.filter(i=>i!==t);o&&(o.length===0?T(this,Qe).delete(n):T(this,Qe).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=T(this,Qe).get(Bi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=T(this,Qe).get(Bi(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Ie.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...T(this,Qe).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Ff(n,r))}findAll(t={}){return this.getAll().filter(n=>Ff(t,n))}notify(t){Ie.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Ie.batch(()=>Promise.all(t.map(n=>n.continue().catch(pt))))}},Qe=new WeakMap,li=new WeakMap,mp);function Bi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Uf(e){return{onFetch:(t,n)=>{var f,d,c,y,x;const r=t.options,o=(c=(d=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:d.fetchMore)==null?void 0:c.direction,i=((y=t.state.data)==null?void 0:y.pages)||[],s=((x=t.state.data)==null?void 0:x.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const u=async()=>{let p=!1;const S=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(t.signal.aborted?p=!0:t.signal.addEventListener("abort",()=>{p=!0}),t.signal)})},m=Yv(t.options,t.fetchOptions),v=async(w,E,C)=>{if(p)return Promise.reject();if(E==null&&w.pages.length)return Promise.resolve(w);const k={queryKey:t.queryKey,pageParam:E,direction:C?"backward":"forward",meta:t.options.meta};S(k);const N=await m(k),{maxPages:P}=t.options,M=C?YE:GE;return{pages:M(w.pages,N,P),pageParams:M(w.pageParams,E,P)}};if(o&&i.length){const w=o==="backward",E=w?lC:Bf,C={pages:i,pageParams:s},k=E(r,C);a=await v(C,k,w)}else{const w=e??i.length;do{const E=l===0?s[0]??r.initialPageParam:Bf(r,a);if(l>0&&E==null)break;a=await v(a,E),l++}while(l<w)}return a};t.options.persister?t.fetchFn=()=>{var p,S;return(S=(p=t.options).persister)==null?void 0:S.call(p,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Bf(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function lC(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var he,vn,gn,Hr,Qr,yn,Kr,Gr,vp,uC=(vp=class{constructor(e={}){Z(this,he);Z(this,vn);Z(this,gn);Z(this,Hr);Z(this,Qr);Z(this,yn);Z(this,Kr);Z(this,Gr);H(this,he,e.queryCache||new oC),H(this,vn,e.mutationCache||new aC),H(this,gn,e.defaultOptions||{}),H(this,Hr,new Map),H(this,Qr,new Map),H(this,yn,0)}mount(){xi(this,yn)._++,T(this,yn)===1&&(H(this,Kr,Xv.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,he).onFocus())})),H(this,Gr,$s.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,he).onOnline())})))}unmount(){var e,t;xi(this,yn)._--,T(this,yn)===0&&((e=T(this,Kr))==null||e.call(this),H(this,Kr,void 0),(t=T(this,Gr))==null||t.call(this),H(this,Gr,void 0))}isFetching(e){return T(this,he).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return T(this,vn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,he).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=T(this,he).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Df(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return T(this,he).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=T(this,he).get(r.queryHash),i=o==null?void 0:o.state.data,s=BE(t,i);if(s!==void 0)return T(this,he).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Ie.batch(()=>T(this,he).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,he).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=T(this,he);Ie.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=T(this,he),r={type:"active",...e};return Ie.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Ie.batch(()=>T(this,he).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(pt).catch(pt)}invalidateQueries(e={},t={}){return Ie.batch(()=>{if(T(this,he).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Ie.batch(()=>T(this,he).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(pt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(pt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=T(this,he).build(this,t);return n.isStaleByTime(Df(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(pt).catch(pt)}fetchInfiniteQuery(e){return e.behavior=Uf(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(pt).catch(pt)}ensureInfiniteQueryData(e){return e.behavior=Uf(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return $s.isOnline()?T(this,vn).resumePausedMutations():Promise.resolve()}getQueryCache(){return T(this,he)}getMutationCache(){return T(this,vn)}getDefaultOptions(){return T(this,gn)}setDefaultOptions(e){H(this,gn,e)}setQueryDefaults(e,t){T(this,Hr).set(ii(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...T(this,Hr).values()];let n={};return t.forEach(r=>{si(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){T(this,Qr).set(ii(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...T(this,Qr).values()];let n={};return t.forEach(r=>{si(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...T(this,gn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=zc(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===$c&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...T(this,gn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){T(this,he).clear(),T(this,vn).clear()}},he=new WeakMap,vn=new WeakMap,gn=new WeakMap,Hr=new WeakMap,Qr=new WeakMap,yn=new WeakMap,Kr=new WeakMap,Gr=new WeakMap,vp),cC=h.createContext(void 0),dC=({client:e,children:t})=>(h.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),g.jsx(cC.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Us(){return Us=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Us.apply(this,arguments)}var Sn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Sn||(Sn={}));const Vf="popstate";function fC(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return bu("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:ng(o)}return hC(t,n,null,e)}function We(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function tg(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function pC(){return Math.random().toString(36).substr(2,8)}function Wf(e,t){return{usr:e.state,key:e.key,idx:t}}function bu(e,t,n,r){return n===void 0&&(n=null),Us({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Sa(t):t,{state:n,key:t&&t.key||r||pC()})}function ng(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Sa(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function hC(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=Sn.Pop,l=null,u=f();u==null&&(u=0,s.replaceState(Us({},s.state,{idx:u}),""));function f(){return(s.state||{idx:null}).idx}function d(){a=Sn.Pop;let S=f(),m=S==null?null:S-u;u=S,l&&l({action:a,location:p.location,delta:m})}function c(S,m){a=Sn.Push;let v=bu(p.location,S,m);u=f()+1;let w=Wf(v,u),E=p.createHref(v);try{s.pushState(w,"",E)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(E)}i&&l&&l({action:a,location:p.location,delta:1})}function y(S,m){a=Sn.Replace;let v=bu(p.location,S,m);u=f();let w=Wf(v,u),E=p.createHref(v);s.replaceState(w,"",E),i&&l&&l({action:a,location:p.location,delta:0})}function x(S){let m=o.location.origin!=="null"?o.location.origin:o.location.href,v=typeof S=="string"?S:ng(S);return v=v.replace(/ $/,"%20"),We(m,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,m)}let p={get action(){return a},get location(){return e(o,s)},listen(S){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(Vf,d),l=S,()=>{o.removeEventListener(Vf,d),l=null}},createHref(S){return t(o,S)},createURL:x,encodeLocation(S){let m=x(S);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:c,replace:y,go(S){return s.go(S)}};return p}var Hf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Hf||(Hf={}));function mC(e,t,n){return n===void 0&&(n="/"),vC(e,t,n,!1)}function vC(e,t,n,r){let o=typeof t=="string"?Sa(t):t,i=ig(o.pathname||"/",n);if(i==null)return null;let s=rg(e);gC(s);let a=null;for(let l=0;a==null&&l<s.length;++l){let u=TC(i);a=PC(s[l],u,r)}return a}function rg(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(We(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Fr([r,l.relativePath]),f=n.concat(l);i.children&&i.children.length>0&&(We(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),rg(i.children,t,f,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:bC(u,i.index),routesMeta:f})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of og(i.path))o(i,s,l)}),t}function og(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=og(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function gC(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:kC(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const yC=/^:[\w-]+$/,wC=3,xC=2,SC=1,EC=10,CC=-2,Qf=e=>e==="*";function bC(e,t){let n=e.split("/"),r=n.length;return n.some(Qf)&&(r+=CC),t&&(r+=xC),n.filter(o=>!Qf(o)).reduce((o,i)=>o+(yC.test(i)?wC:i===""?SC:EC),r)}function kC(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function PC(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",d=Kf({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},f),c=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Kf({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},f)),!d)return null;Object.assign(o,d.params),s.push({params:o,pathname:Fr([i,d.pathname]),pathnameBase:RC(Fr([i,d.pathnameBase])),route:c}),d.pathnameBase!=="/"&&(i=Fr([i,d.pathnameBase]))}return s}function Kf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=NC(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,f,d)=>{let{paramName:c,isOptional:y}=f;if(c==="*"){let p=a[d]||"";s=i.slice(0,i.length-p.length).replace(/(.)\/+$/,"$1")}const x=a[d];return y&&!x?u[c]=void 0:u[c]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function NC(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),tg(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function TC(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return tg(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ig(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const Fr=e=>e.join("/").replace(/\/\/+/g,"/"),RC=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function _C(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const sg=["post","put","patch","delete"];new Set(sg);const OC=["get",...sg];new Set(OC);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Bs(){return Bs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Bs.apply(this,arguments)}const AC=h.createContext(null),jC=h.createContext(null),ag=h.createContext(null),Ea=h.createContext(null),Ca=h.createContext({outlet:null,matches:[],isDataRoute:!1}),lg=h.createContext(null);function Uc(){return h.useContext(Ea)!=null}function ug(){return Uc()||We(!1),h.useContext(Ea).location}function MC(e,t){return IC(e,t)}function IC(e,t,n,r){Uc()||We(!1);let{navigator:o}=h.useContext(ag),{matches:i}=h.useContext(Ca),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=ug(),f;if(t){var d;let S=typeof t=="string"?Sa(t):t;l==="/"||(d=S.pathname)!=null&&d.startsWith(l)||We(!1),f=S}else f=u;let c=f.pathname||"/",y=c;if(l!=="/"){let S=l.replace(/^\//,"").split("/");y="/"+c.replace(/^\//,"").split("/").slice(S.length).join("/")}let x=mC(e,{pathname:y}),p=$C(x&&x.map(S=>Object.assign({},S,{params:Object.assign({},a,S.params),pathname:Fr([l,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?l:Fr([l,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),i,n,r);return t&&p?h.createElement(Ea.Provider,{value:{location:Bs({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:Sn.Pop}},p):p}function DC(){let e=WC(),t=_C(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:o},n):null,null)}const LC=h.createElement(DC,null);class FC extends h.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?h.createElement(Ca.Provider,{value:this.props.routeContext},h.createElement(lg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function zC(e){let{routeContext:t,match:n,children:r}=e,o=h.useContext(AC);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),h.createElement(Ca.Provider,{value:t},r)}function $C(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let f=s.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);f>=0||We(!1),s=s.slice(0,Math.min(s.length,f+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<s.length;f++){let d=s[f];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=f),d.route.id){let{loaderData:c,errors:y}=n,x=d.route.loader&&c[d.route.id]===void 0&&(!y||y[d.route.id]===void 0);if(d.route.lazy||x){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((f,d,c)=>{let y,x=!1,p=null,S=null;n&&(y=a&&d.route.id?a[d.route.id]:void 0,p=d.route.errorElement||LC,l&&(u<0&&c===0?(x=!0,S=null):u===c&&(x=!0,S=d.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,c+1)),v=()=>{let w;return y?w=p:x?w=S:d.route.Component?w=h.createElement(d.route.Component,null):d.route.element?w=d.route.element:w=f,h.createElement(zC,{match:d,routeContext:{outlet:f,matches:m,isDataRoute:n!=null},children:w})};return n&&(d.route.ErrorBoundary||d.route.errorElement||c===0)?h.createElement(FC,{location:n.location,revalidation:n.revalidation,component:p,error:y,children:v(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):v()},null)}var ku=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ku||{});function UC(e){let t=h.useContext(jC);return t||We(!1),t}function BC(e){let t=h.useContext(Ca);return t||We(!1),t}function VC(e){let t=BC(),n=t.matches[t.matches.length-1];return n.route.id||We(!1),n.route.id}function WC(){var e;let t=h.useContext(lg),n=UC(ku.UseRouteError),r=VC(ku.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Pu(e){We(!1)}function HC(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Sn.Pop,navigator:i,static:s=!1,future:a}=e;Uc()&&We(!1);let l=t.replace(/^\/*/,"/"),u=h.useMemo(()=>({basename:l,navigator:i,static:s,future:Bs({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=Sa(r));let{pathname:f="/",search:d="",hash:c="",state:y=null,key:x="default"}=r,p=h.useMemo(()=>{let S=ig(f,l);return S==null?null:{location:{pathname:S,search:d,hash:c,state:y,key:x},navigationType:o}},[l,f,d,c,y,x,o]);return p==null?null:h.createElement(ag.Provider,{value:u},h.createElement(Ea.Provider,{children:n,value:p}))}function QC(e){let{children:t,location:n}=e;return MC(Nu(t),n)}new Promise(()=>{});function Nu(e,t){t===void 0&&(t=[]);let n=[];return h.Children.forEach(e,(r,o)=>{if(!h.isValidElement(r))return;let i=[...t,o];if(r.type===h.Fragment){n.push.apply(n,Nu(r.props.children,i));return}r.type!==Pu&&We(!1),!r.props.index||!r.props.children||We(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Nu(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const KC="6";try{window.__reactRouterVersion=KC}catch{}const GC="startTransition",Gf=Tp[GC];function YC(e){let{basename:t,children:n,future:r,window:o}=e,i=h.useRef();i.current==null&&(i.current=fC({window:o,v5Compat:!0}));let s=i.current,[a,l]=h.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},f=h.useCallback(d=>{u&&Gf?Gf(()=>l(d)):l(d)},[l,u]);return h.useLayoutEffect(()=>s.listen(f),[s,f]),h.createElement(HC,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}var Yf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Yf||(Yf={}));var Xf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Xf||(Xf={}));const Fo=h.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:ne("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Fo.displayName="Card";const Tu=h.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:ne("flex flex-col space-y-1.5 p-6",e),...t}));Tu.displayName="CardHeader";const Ru=h.forwardRef(({className:e,...t},n)=>g.jsx("h3",{ref:n,className:ne("text-2xl font-semibold leading-none tracking-tight",e),...t}));Ru.displayName="CardTitle";const XC=h.forwardRef(({className:e,...t},n)=>g.jsx("p",{ref:n,className:ne("text-sm text-muted-foreground",e),...t}));XC.displayName="CardDescription";const zo=h.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:ne("p-6 pt-0",e),...t}));zo.displayName="CardContent";const qC=h.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:ne("flex items-center p-6 pt-0",e),...t}));qC.displayName="CardFooter";function ZC(e,t=[]){let n=[];function r(i,s){const a=h.createContext(s),l=n.length;n=[...n,s];function u(d){const{scope:c,children:y,...x}=d,p=(c==null?void 0:c[e][l])||a,S=h.useMemo(()=>x,Object.values(x));return g.jsx(p.Provider,{value:S,children:y})}function f(d,c){const y=(c==null?void 0:c[e][l])||a,x=h.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>h.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,JC(o,...t)]}function JC(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Bc="Progress",Vc=100,[eb,Zk]=ZC(Bc),[tb,nb]=eb(Bc),cg=h.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:i=rb,...s}=e;(o||o===0)&&!qf(o)&&console.error(ob(`${o}`,"Progress"));const a=qf(o)?o:Vc;r!==null&&!Zf(r,a)&&console.error(ib(`${r}`,"Progress"));const l=Zf(r,a)?r:null,u=Vs(l)?i(l,a):void 0;return g.jsx(tb,{scope:n,value:l,max:a,children:g.jsx(te.div,{"aria-valuemax":a,"aria-valuemin":0,"aria-valuenow":Vs(l)?l:void 0,"aria-valuetext":u,role:"progressbar","data-state":pg(l,a),"data-value":l??void 0,"data-max":a,...s,ref:t})})});cg.displayName=Bc;var dg="ProgressIndicator",fg=h.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=nb(dg,n);return g.jsx(te.div,{"data-state":pg(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});fg.displayName=dg;function rb(e,t){return`${Math.round(e/t*100)}%`}function pg(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Vs(e){return typeof e=="number"}function qf(e){return Vs(e)&&!isNaN(e)&&e>0}function Zf(e,t){return Vs(e)&&!isNaN(e)&&e<=t&&e>=0}function ob(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Vc}\`.`}function ib(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Vc} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var hg=cg,sb=fg;const mg=h.forwardRef(({className:e,value:t,...n},r)=>g.jsx(hg,{ref:r,className:ne("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...n,children:g.jsx(sb,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));mg.displayName=hg.displayName;function ab({current:e,goals:t}){const n=[{name:"Calories",current:e.calories,goal:t.calories,color:"calories",unit:"cal"},{name:"Protein",current:e.protein,goal:t.protein,color:"protein",unit:"g"},{name:"Carbs",current:e.carbs,goal:t.carbs,color:"carbs",unit:"g"},{name:"Fats",current:e.fats,goal:t.fats,color:"fats",unit:"g"}];return g.jsx("div",{className:"space-y-6",children:n.map(r=>{const o=Math.min(r.current/r.goal*100,100);return g.jsxs("div",{className:"space-y-2",children:[g.jsxs("div",{className:"flex justify-between items-center",children:[g.jsx("span",{className:"font-medium text-foreground",children:r.name}),g.jsxs("span",{className:"text-sm text-muted-foreground",children:[Math.round(r.current),"/",r.goal,r.unit]})]}),g.jsxs("div",{className:"relative",children:[g.jsx(mg,{value:o,className:"h-3",style:{background:`hsl(var(--${r.color}) / 0.2)`}}),g.jsx("div",{className:"absolute top-0 left-0 h-3 rounded-full transition-all duration-500",style:{width:`${o}%`,background:`hsl(var(--${r.color}))`}})]})]},r.name)})})}const lb=pa("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function ub({className:e,variant:t,...n}){return g.jsx("div",{className:ne(lb({variant:t}),e),...n})}function cb({entry:e}){const t=r=>{switch(r){case"voice":return g.jsx(mu,{className:"w-3 h-3"});case"image":return g.jsx(lv,{className:"w-3 h-3"});case"text":return g.jsx(uv,{className:"w-3 h-3"})}},n=r=>{switch(r){case"voice":return"bg-protein/10 text-protein";case"image":return"bg-carbs/10 text-carbs";case"text":return"bg-fats/10 text-fats"}};return g.jsx(Fo,{className:"transition-all duration-200 hover:shadow-md",children:g.jsxs(zo,{className:"p-4",children:[g.jsxs("div",{className:"flex justify-between items-start mb-3",children:[g.jsxs("div",{children:[g.jsx("h3",{className:"font-semibold text-foreground",children:e.name}),g.jsx("p",{className:"text-sm text-muted-foreground",children:e.quantity})]}),g.jsxs(ub,{variant:"secondary",className:`${n(e.method)} flex items-center gap-1`,children:[t(e.method),e.method]})]}),g.jsxs("div",{className:"grid grid-cols-4 gap-2 text-center",children:[g.jsxs("div",{className:"space-y-1",children:[g.jsx("p",{className:"text-xs text-muted-foreground",children:"Cal"}),g.jsx("p",{className:"font-semibold text-calories",children:Math.round(e.macros.calories)})]}),g.jsxs("div",{className:"space-y-1",children:[g.jsx("p",{className:"text-xs text-muted-foreground",children:"Protein"}),g.jsxs("p",{className:"font-semibold text-protein",children:[Math.round(e.macros.protein),"g"]})]}),g.jsxs("div",{className:"space-y-1",children:[g.jsx("p",{className:"text-xs text-muted-foreground",children:"Carbs"}),g.jsxs("p",{className:"font-semibold text-carbs",children:[Math.round(e.macros.carbs),"g"]})]}),g.jsxs("div",{className:"space-y-1",children:[g.jsx("p",{className:"text-xs text-muted-foreground",children:"Fats"}),g.jsxs("p",{className:"font-semibold text-fats",children:[Math.round(e.macros.fats),"g"]})]})]}),g.jsx("p",{className:"text-xs text-muted-foreground mt-3",children:new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})})}var cl="focusScope.autoFocusOnMount",dl="focusScope.autoFocusOnUnmount",Jf={bubbles:!1,cancelable:!0},db="FocusScope",vg=h.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[a,l]=h.useState(null),u=et(o),f=et(i),d=h.useRef(null),c=Ae(t,p=>l(p)),y=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(r){let p=function(w){if(y.paused||!a)return;const E=w.target;a.contains(E)?d.current=E:ln(d.current,{select:!0})},S=function(w){if(y.paused||!a)return;const E=w.relatedTarget;E!==null&&(a.contains(E)||ln(d.current,{select:!0}))},m=function(w){if(document.activeElement===document.body)for(const C of w)C.removedNodes.length>0&&ln(a)};document.addEventListener("focusin",p),document.addEventListener("focusout",S);const v=new MutationObserver(m);return a&&v.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",S),v.disconnect()}}},[r,a,y.paused]),h.useEffect(()=>{if(a){tp.add(y);const p=document.activeElement;if(!a.contains(p)){const m=new CustomEvent(cl,Jf);a.addEventListener(cl,u),a.dispatchEvent(m),m.defaultPrevented||(fb(gb(gg(a)),{select:!0}),document.activeElement===p&&ln(a))}return()=>{a.removeEventListener(cl,u),setTimeout(()=>{const m=new CustomEvent(dl,Jf);a.addEventListener(dl,f),a.dispatchEvent(m),m.defaultPrevented||ln(p??document.body,{select:!0}),a.removeEventListener(dl,f),tp.remove(y)},0)}}},[a,u,f,y]);const x=h.useCallback(p=>{if(!n&&!r||y.paused)return;const S=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,m=document.activeElement;if(S&&m){const v=p.currentTarget,[w,E]=pb(v);w&&E?!p.shiftKey&&m===E?(p.preventDefault(),n&&ln(w,{select:!0})):p.shiftKey&&m===w&&(p.preventDefault(),n&&ln(E,{select:!0})):m===v&&p.preventDefault()}},[n,r,y.paused]);return g.jsx(te.div,{tabIndex:-1,...s,ref:c,onKeyDown:x})});vg.displayName=db;function fb(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ln(r,{select:t}),document.activeElement!==n)return}function pb(e){const t=gg(e),n=ep(t,e),r=ep(t.reverse(),e);return[n,r]}function gg(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ep(e,t){for(const n of e)if(!hb(n,{upTo:t}))return n}function hb(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function mb(e){return e instanceof HTMLInputElement&&"select"in e}function ln(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&mb(e)&&t&&e.select()}}var tp=vb();function vb(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=np(e,t),e.unshift(t)},remove(t){var n;e=np(e,t),(n=e[0])==null||n.resume()}}}function np(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function gb(e){return e.filter(t=>t.tagName!=="A")}var fl=0;function yb(){h.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??rp()),document.body.insertAdjacentElement("beforeend",e[1]??rp()),fl++,()=>{fl===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),fl--}},[])}function rp(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ot=function(){return Ot=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Ot.apply(this,arguments)};function yg(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function wb(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var ls="right-scroll-bar-position",us="width-before-scroll-bar",xb="with-scroll-bars-hidden",Sb="--removed-body-scroll-bar-size";function pl(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Eb(e,t){var n=h.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Cb=typeof window<"u"?h.useLayoutEffect:h.useEffect,op=new WeakMap;function bb(e,t){var n=Eb(null,function(r){return e.forEach(function(o){return pl(o,r)})});return Cb(function(){var r=op.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(a){i.has(a)||pl(a,null)}),i.forEach(function(a){o.has(a)||pl(a,s)})}op.set(n,e)},[e]),n}function kb(e){return e}function Pb(e,t){t===void 0&&(t=kb);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(a){return a!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var a=n;n=[],a.forEach(i),s=n}var l=function(){var f=s;s=[],f.forEach(i)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(f){s.push(f),u()},filter:function(f){return s=s.filter(f),n}}}};return o}function Nb(e){e===void 0&&(e={});var t=Pb(null);return t.options=Ot({async:!0,ssr:!1},e),t}var wg=function(e){var t=e.sideCar,n=yg(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return h.createElement(r,Ot({},n))};wg.isSideCarExport=!0;function Tb(e,t){return e.useMedium(t),wg}var xg=Nb(),hl=function(){},ba=h.forwardRef(function(e,t){var n=h.useRef(null),r=h.useState({onScrollCapture:hl,onWheelCapture:hl,onTouchMoveCapture:hl}),o=r[0],i=r[1],s=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,f=e.enabled,d=e.shards,c=e.sideCar,y=e.noIsolation,x=e.inert,p=e.allowPinchZoom,S=e.as,m=S===void 0?"div":S,v=e.gapMode,w=yg(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=c,C=bb([n,t]),k=Ot(Ot({},w),o);return h.createElement(h.Fragment,null,f&&h.createElement(E,{sideCar:xg,removeScrollBar:u,shards:d,noIsolation:y,inert:x,setCallbacks:i,allowPinchZoom:!!p,lockRef:n,gapMode:v}),s?h.cloneElement(h.Children.only(a),Ot(Ot({},k),{ref:C})):h.createElement(m,Ot({},k,{className:l,ref:C}),a))});ba.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ba.classNames={fullWidth:us,zeroRight:ls};var Rb=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function _b(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Rb();return t&&e.setAttribute("nonce",t),e}function Ob(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Ab(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var jb=function(){var e=0,t=null;return{add:function(n){e==0&&(t=_b())&&(Ob(t,n),Ab(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Mb=function(){var e=jb();return function(t,n){h.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Sg=function(){var e=Mb(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Ib={left:0,top:0,right:0,gap:0},ml=function(e){return parseInt(e||"",10)||0},Db=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[ml(n),ml(r),ml(o)]},Lb=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ib;var t=Db(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Fb=Sg(),zr="data-scroll-locked",zb=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(xb,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(zr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ls,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(us,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ls," .").concat(ls,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(us," .").concat(us,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(zr,`] {
    `).concat(Sb,": ").concat(a,`px;
  }
`)},ip=function(){var e=parseInt(document.body.getAttribute(zr)||"0",10);return isFinite(e)?e:0},$b=function(){h.useEffect(function(){return document.body.setAttribute(zr,(ip()+1).toString()),function(){var e=ip()-1;e<=0?document.body.removeAttribute(zr):document.body.setAttribute(zr,e.toString())}},[])},Ub=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;$b();var i=h.useMemo(function(){return Lb(o)},[o]);return h.createElement(Fb,{styles:zb(i,!t,o,n?"":"!important")})},_u=!1;if(typeof window<"u")try{var Vi=Object.defineProperty({},"passive",{get:function(){return _u=!0,!0}});window.addEventListener("test",Vi,Vi),window.removeEventListener("test",Vi,Vi)}catch{_u=!1}var pr=_u?{passive:!1}:!1,Bb=function(e){return e.tagName==="TEXTAREA"},Eg=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Bb(e)&&n[t]==="visible")},Vb=function(e){return Eg(e,"overflowY")},Wb=function(e){return Eg(e,"overflowX")},sp=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Cg(e,r);if(o){var i=bg(e,r),s=i[1],a=i[2];if(s>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Hb=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Qb=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Cg=function(e,t){return e==="v"?Vb(t):Wb(t)},bg=function(e,t){return e==="v"?Hb(t):Qb(t)},Kb=function(e,t){return e==="h"&&t==="rtl"?-1:1},Gb=function(e,t,n,r,o){var i=Kb(e,window.getComputedStyle(t).direction),s=i*r,a=n.target,l=t.contains(a),u=!1,f=s>0,d=0,c=0;do{var y=bg(e,a),x=y[0],p=y[1],S=y[2],m=p-S-i*x;(x||m)&&Cg(e,a)&&(d+=m,c+=x),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(f&&(Math.abs(d)<1||!o)||!f&&(Math.abs(c)<1||!o))&&(u=!0),u},Wi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ap=function(e){return[e.deltaX,e.deltaY]},lp=function(e){return e&&"current"in e?e.current:e},Yb=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Xb=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},qb=0,hr=[];function Zb(e){var t=h.useRef([]),n=h.useRef([0,0]),r=h.useRef(),o=h.useState(qb++)[0],i=h.useState(Sg)[0],s=h.useRef(e);h.useEffect(function(){s.current=e},[e]),h.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var p=wb([e.lockRef.current],(e.shards||[]).map(lp),!0).filter(Boolean);return p.forEach(function(S){return S.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),p.forEach(function(S){return S.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=h.useCallback(function(p,S){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!s.current.allowPinchZoom;var m=Wi(p),v=n.current,w="deltaX"in p?p.deltaX:v[0]-m[0],E="deltaY"in p?p.deltaY:v[1]-m[1],C,k=p.target,N=Math.abs(w)>Math.abs(E)?"h":"v";if("touches"in p&&N==="h"&&k.type==="range")return!1;var P=sp(N,k);if(!P)return!0;if(P?C=N:(C=N==="v"?"h":"v",P=sp(N,k)),!P)return!1;if(!r.current&&"changedTouches"in p&&(w||E)&&(r.current=C),!C)return!0;var M=r.current||C;return Gb(M,S,p,M==="h"?w:E,!0)},[]),l=h.useCallback(function(p){var S=p;if(!(!hr.length||hr[hr.length-1]!==i)){var m="deltaY"in S?ap(S):Wi(S),v=t.current.filter(function(C){return C.name===S.type&&(C.target===S.target||S.target===C.shadowParent)&&Yb(C.delta,m)})[0];if(v&&v.should){S.cancelable&&S.preventDefault();return}if(!v){var w=(s.current.shards||[]).map(lp).filter(Boolean).filter(function(C){return C.contains(S.target)}),E=w.length>0?a(S,w[0]):!s.current.noIsolation;E&&S.cancelable&&S.preventDefault()}}},[]),u=h.useCallback(function(p,S,m,v){var w={name:p,delta:S,target:m,should:v,shadowParent:Jb(m)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(E){return E!==w})},1)},[]),f=h.useCallback(function(p){n.current=Wi(p),r.current=void 0},[]),d=h.useCallback(function(p){u(p.type,ap(p),p.target,a(p,e.lockRef.current))},[]),c=h.useCallback(function(p){u(p.type,Wi(p),p.target,a(p,e.lockRef.current))},[]);h.useEffect(function(){return hr.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:c}),document.addEventListener("wheel",l,pr),document.addEventListener("touchmove",l,pr),document.addEventListener("touchstart",f,pr),function(){hr=hr.filter(function(p){return p!==i}),document.removeEventListener("wheel",l,pr),document.removeEventListener("touchmove",l,pr),document.removeEventListener("touchstart",f,pr)}},[]);var y=e.removeScrollBar,x=e.inert;return h.createElement(h.Fragment,null,x?h.createElement(i,{styles:Xb(o)}):null,y?h.createElement(Ub,{gapMode:e.gapMode}):null)}function Jb(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ek=Tb(xg,Zb);var kg=h.forwardRef(function(e,t){return h.createElement(ba,Ot({},e,{ref:t,sideCar:ek}))});kg.classNames=ba.classNames;var tk=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},mr=new WeakMap,Hi=new WeakMap,Qi={},vl=0,Pg=function(e){return e&&(e.host||Pg(e.parentNode))},nk=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Pg(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},rk=function(e,t,n,r){var o=nk(t,Array.isArray(e)?e:[e]);Qi[n]||(Qi[n]=new WeakMap);var i=Qi[n],s=[],a=new Set,l=new Set(o),u=function(d){!d||a.has(d)||(a.add(d),u(d.parentNode))};o.forEach(u);var f=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(c){if(a.has(c))f(c);else try{var y=c.getAttribute(r),x=y!==null&&y!=="false",p=(mr.get(c)||0)+1,S=(i.get(c)||0)+1;mr.set(c,p),i.set(c,S),s.push(c),p===1&&x&&Hi.set(c,!0),S===1&&c.setAttribute(n,"true"),x||c.setAttribute(r,"true")}catch(m){console.error("aria-hidden: cannot operate on ",c,m)}})};return f(t),a.clear(),vl++,function(){s.forEach(function(d){var c=mr.get(d)-1,y=i.get(d)-1;mr.set(d,c),i.set(d,y),c||(Hi.has(d)||d.removeAttribute(r),Hi.delete(d)),y||d.removeAttribute(n)}),vl--,vl||(mr=new WeakMap,mr=new WeakMap,Hi=new WeakMap,Qi={})}},ok=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=tk(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),rk(r,o,n,"aria-hidden")):function(){return null}},Wc="Dialog",[Ng,Jk]=aa(Wc),[ik,Et]=Ng(Wc),Tg=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,a=h.useRef(null),l=h.useRef(null),[u=!1,f]=ua({prop:r,defaultProp:o,onChange:i});return g.jsx(ik,{scope:t,triggerRef:a,contentRef:l,contentId:Lo(),titleId:Lo(),descriptionId:Lo(),open:u,onOpenChange:f,onOpenToggle:h.useCallback(()=>f(d=>!d),[f]),modal:s,children:n})};Tg.displayName=Wc;var Rg="DialogTrigger",_g=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Et(Rg,n),i=Ae(t,o.triggerRef);return g.jsx(te.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Kc(o.open),...r,ref:i,onClick:X(e.onClick,o.onOpenToggle)})});_g.displayName=Rg;var Hc="DialogPortal",[sk,Og]=Ng(Hc,{forceMount:void 0}),Ag=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=Et(Hc,t);return g.jsx(sk,{scope:t,forceMount:n,children:h.Children.map(r,s=>g.jsx(ar,{present:n||i.open,children:g.jsx(Pc,{asChild:!0,container:o,children:s})}))})};Ag.displayName=Hc;var Ws="DialogOverlay",jg=h.forwardRef((e,t)=>{const n=Og(Ws,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Et(Ws,e.__scopeDialog);return i.modal?g.jsx(ar,{present:r||i.open,children:g.jsx(ak,{...o,ref:t})}):null});jg.displayName=Ws;var ak=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Et(Ws,n);return g.jsx(kg,{as:no,allowPinchZoom:!0,shards:[o.contentRef],children:g.jsx(te.div,{"data-state":Kc(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),or="DialogContent",Mg=h.forwardRef((e,t)=>{const n=Og(or,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Et(or,e.__scopeDialog);return g.jsx(ar,{present:r||i.open,children:i.modal?g.jsx(lk,{...o,ref:t}):g.jsx(uk,{...o,ref:t})})});Mg.displayName=or;var lk=h.forwardRef((e,t)=>{const n=Et(or,e.__scopeDialog),r=h.useRef(null),o=Ae(t,n.contentRef,r);return h.useEffect(()=>{const i=r.current;if(i)return ok(i)},[]),g.jsx(Ig,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:X(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:X(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,a=s.button===0&&s.ctrlKey===!0;(s.button===2||a)&&i.preventDefault()}),onFocusOutside:X(e.onFocusOutside,i=>i.preventDefault())})}),uk=h.forwardRef((e,t)=>{const n=Et(or,e.__scopeDialog),r=h.useRef(!1),o=h.useRef(!1);return g.jsx(Ig,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,a;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const s=i.target;((u=n.triggerRef.current)==null?void 0:u.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}})}),Ig=h.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,a=Et(or,n),l=h.useRef(null),u=Ae(t,l);return yb(),g.jsxs(g.Fragment,{children:[g.jsx(vg,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:g.jsx(la,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Kc(a.open),...s,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),g.jsxs(g.Fragment,{children:[g.jsx(ck,{titleId:a.titleId}),g.jsx(fk,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Qc="DialogTitle",Dg=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Et(Qc,n);return g.jsx(te.h2,{id:o.titleId,...r,ref:t})});Dg.displayName=Qc;var Lg="DialogDescription",Fg=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Et(Lg,n);return g.jsx(te.p,{id:o.descriptionId,...r,ref:t})});Fg.displayName=Lg;var zg="DialogClose",$g=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Et(zg,n);return g.jsx(te.button,{type:"button",...r,ref:t,onClick:X(e.onClick,()=>o.onOpenChange(!1))})});$g.displayName=zg;function Kc(e){return e?"open":"closed"}var Ug="DialogTitleWarning",[e2,Bg]=dx(Ug,{contentName:or,titleName:Qc,docsSlug:"dialog"}),ck=({titleId:e})=>{const t=Bg(Ug),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return h.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},dk="DialogDescriptionWarning",fk=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Bg(dk).contentName}}.`;return h.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},pk=Tg,hk=_g,mk=Ag,Vg=jg,Wg=Mg,Hg=Dg,Qg=Fg,vk=$g;const Kg=pk,Gg=hk,gk=mk,Yg=h.forwardRef(({className:e,...t},n)=>g.jsx(Vg,{ref:n,className:ne("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Yg.displayName=Vg.displayName;const Gc=h.forwardRef(({className:e,children:t,...n},r)=>g.jsxs(gk,{children:[g.jsx(Yg,{}),g.jsxs(Wg,{ref:r,className:ne("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,g.jsxs(vk,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[g.jsx(cv,{className:"h-4 w-4"}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Gc.displayName=Wg.displayName;const Yc=({className:e,...t})=>g.jsx("div",{className:ne("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Yc.displayName="DialogHeader";const Xc=h.forwardRef(({className:e,...t},n)=>g.jsx(Hg,{ref:n,className:ne("text-lg font-semibold leading-none tracking-tight",e),...t}));Xc.displayName=Hg.displayName;const qc=h.forwardRef(({className:e,...t},n)=>g.jsx(Qg,{ref:n,className:ne("text-sm text-muted-foreground",e),...t}));qc.displayName=Qg.displayName;const yk=pa("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),$t=h.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const s=r?no:"button";return g.jsx(s,{className:ne(yk({variant:t,size:n,className:e})),ref:i,...o})});$t.displayName="Button";const Tr=h.forwardRef(({className:e,type:t,...n},r)=>g.jsx("input",{type:t,className:ne("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));Tr.displayName="Input";const Xg=h.forwardRef(({className:e,...t},n)=>g.jsx("textarea",{className:ne("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Xg.displayName="Textarea";function wk(e,t=[]){let n=[];function r(i,s){const a=h.createContext(s),l=n.length;n=[...n,s];function u(d){const{scope:c,children:y,...x}=d,p=(c==null?void 0:c[e][l])||a,S=h.useMemo(()=>x,Object.values(x));return g.jsx(p.Provider,{value:S,children:y})}function f(d,c){const y=(c==null?void 0:c[e][l])||a,x=h.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>h.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,xk(o,...t)]}function xk(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Sk=h.createContext(void 0);function qg(e){const t=h.useContext(Sk);return e||t||"ltr"}var gl="rovingFocusGroup.onEntryFocus",Ek={bubbles:!1,cancelable:!0},ka="RovingFocusGroup",[Ou,Zg,Ck]=Im(ka),[bk,Jg]=wk(ka,[Ck]),[kk,Pk]=bk(ka),ey=h.forwardRef((e,t)=>g.jsx(Ou.Provider,{scope:e.__scopeRovingFocusGroup,children:g.jsx(Ou.Slot,{scope:e.__scopeRovingFocusGroup,children:g.jsx(Nk,{...e,ref:t})})}));ey.displayName=ka;var Nk=h.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...d}=e,c=h.useRef(null),y=Ae(t,c),x=qg(i),[p=null,S]=ua({prop:s,defaultProp:a,onChange:l}),[m,v]=h.useState(!1),w=et(u),E=Zg(n),C=h.useRef(!1),[k,N]=h.useState(0);return h.useEffect(()=>{const P=c.current;if(P)return P.addEventListener(gl,w),()=>P.removeEventListener(gl,w)},[w]),g.jsx(kk,{scope:n,orientation:r,dir:x,loop:o,currentTabStopId:p,onItemFocus:h.useCallback(P=>S(P),[S]),onItemShiftTab:h.useCallback(()=>v(!0),[]),onFocusableItemAdd:h.useCallback(()=>N(P=>P+1),[]),onFocusableItemRemove:h.useCallback(()=>N(P=>P-1),[]),children:g.jsx(te.div,{tabIndex:m||k===0?-1:0,"data-orientation":r,...d,ref:y,style:{outline:"none",...e.style},onMouseDown:X(e.onMouseDown,()=>{C.current=!0}),onFocus:X(e.onFocus,P=>{const M=!C.current;if(P.target===P.currentTarget&&M&&!m){const A=new CustomEvent(gl,Ek);if(P.currentTarget.dispatchEvent(A),!A.defaultPrevented){const $=E().filter(z=>z.focusable),L=$.find(z=>z.active),B=$.find(z=>z.id===p),Q=[L,B,...$].filter(Boolean).map(z=>z.ref.current);ry(Q,f)}}C.current=!1}),onBlur:X(e.onBlur,()=>v(!1))})})}),ty="RovingFocusGroupItem",ny=h.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,...s}=e,a=Lo(),l=i||a,u=Pk(ty,n),f=u.currentTabStopId===l,d=Zg(n),{onFocusableItemAdd:c,onFocusableItemRemove:y}=u;return h.useEffect(()=>{if(r)return c(),()=>y()},[r,c,y]),g.jsx(Ou.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:g.jsx(te.span,{tabIndex:f?0:-1,"data-orientation":u.orientation,...s,ref:t,onMouseDown:X(e.onMouseDown,x=>{r?u.onItemFocus(l):x.preventDefault()}),onFocus:X(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:X(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){u.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const p=_k(x,u.orientation,u.dir);if(p!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let m=d().filter(v=>v.focusable).map(v=>v.ref.current);if(p==="last")m.reverse();else if(p==="prev"||p==="next"){p==="prev"&&m.reverse();const v=m.indexOf(x.currentTarget);m=u.loop?Ok(m,v+1):m.slice(v+1)}setTimeout(()=>ry(m))}})})})});ny.displayName=ty;var Tk={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Rk(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function _k(e,t,n){const r=Rk(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Tk[r]}function ry(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Ok(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Ak=ey,jk=ny,Zc="Tabs",[Mk,t2]=aa(Zc,[Jg]),oy=Jg(),[Ik,Jc]=Mk(Zc),iy=h.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:a,activationMode:l="automatic",...u}=e,f=qg(a),[d,c]=ua({prop:r,onChange:o,defaultProp:i});return g.jsx(Ik,{scope:n,baseId:Lo(),value:d,onValueChange:c,orientation:s,dir:f,activationMode:l,children:g.jsx(te.div,{dir:f,"data-orientation":s,...u,ref:t})})});iy.displayName=Zc;var sy="TabsList",ay=h.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,i=Jc(sy,n),s=oy(n);return g.jsx(Ak,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:r,children:g.jsx(te.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});ay.displayName=sy;var ly="TabsTrigger",uy=h.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,s=Jc(ly,n),a=oy(n),l=fy(s.baseId,r),u=py(s.baseId,r),f=r===s.value;return g.jsx(jk,{asChild:!0,...a,focusable:!o,active:f,children:g.jsx(te.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...i,ref:t,onMouseDown:X(e.onMouseDown,d=>{!o&&d.button===0&&d.ctrlKey===!1?s.onValueChange(r):d.preventDefault()}),onKeyDown:X(e.onKeyDown,d=>{[" ","Enter"].includes(d.key)&&s.onValueChange(r)}),onFocus:X(e.onFocus,()=>{const d=s.activationMode!=="manual";!f&&!o&&d&&s.onValueChange(r)})})})});uy.displayName=ly;var cy="TabsContent",dy=h.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:i,...s}=e,a=Jc(cy,n),l=fy(a.baseId,r),u=py(a.baseId,r),f=r===a.value,d=h.useRef(f);return h.useEffect(()=>{const c=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(c)},[]),g.jsx(ar,{present:o||f,children:({present:c})=>g.jsx(te.div,{"data-state":f?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:u,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:c&&i})})});dy.displayName=cy;function fy(e,t){return`${e}-trigger-${t}`}function py(e,t){return`${e}-content-${t}`}var Dk=iy,hy=ay,my=uy,vy=dy;const Lk=Dk,gy=h.forwardRef(({className:e,...t},n)=>g.jsx(hy,{ref:n,className:ne("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));gy.displayName=hy.displayName;const cs=h.forwardRef(({className:e,...t},n)=>g.jsx(my,{ref:n,className:ne("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));cs.displayName=my.displayName;const ds=h.forwardRef(({className:e,...t},n)=>g.jsx(vy,{ref:n,className:ne("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));ds.displayName=vy.displayName;var Fk="Label",yy=h.forwardRef((e,t)=>g.jsx(te.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));yy.displayName=Fk;var wy=yy;const zk=pa("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Rr=h.forwardRef(({className:e,...t},n)=>g.jsx(wy,{ref:n,className:ne(zk(),e),...t}));Rr.displayName=wy.displayName;function $k({onAddFood:e}){const[t,n]=h.useState(!1),[r,o]=h.useState(!1),[i,s]=h.useState(""),[a,l]=h.useState(!1),u=async(x,p)=>{o(!0);try{const S=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyCf_mH39uXUNaTHrbk0WVrGj3LPzo_73AU",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`Analyze this food description and provide nutritional information: "${x}". 
              Return ONLY a JSON object in this exact format:
              {
                "name": "food name",
                "quantity": "amount (e.g., 1 cup, 100g)",
                "macros": {
                  "calories": number,
                  "protein": number,
                  "carbs": number,
                  "fats": number
                }
              }
              Be specific about quantity and provide realistic macro values.`}]}]})});if(!S.ok)throw new Error(`API Error: ${S.status} - ${S.statusText}`);const m=await S.json();if(!m.candidates||m.candidates.length===0)throw new Error("No response from Gemini API");const w=m.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!w)throw new Error("No JSON found in response");const E=JSON.parse(w[0]);if(!E.name||!E.quantity||!E.macros)throw new Error("Invalid response format from API");return E}catch(S){console.error("Error analyzing food:",S),dn.error(`Failed to analyze food: ${S instanceof Error?S.message:"Unknown error"}`);const m=d(x);return{name:x,quantity:"1 serving",macros:m}}finally{o(!1)}},f=async()=>{if(!i.trim())return;const x=await u(i);e({...x,method:"text"}),s(""),n(!1),dn.success("Food added successfully!")},d=x=>{const p=x.toLowerCase();return p.includes("chicken")||p.includes("turkey")?{calories:165,protein:31,carbs:0,fats:3.6}:p.includes("rice")?{calories:130,protein:2.7,carbs:28,fats:.3}:p.includes("oats")||p.includes("oatmeal")?{calories:389,protein:16.9,carbs:66,fats:6.9}:p.includes("peanut butter")?{calories:588,protein:25,carbs:20,fats:50}:p.includes("milk")?{calories:42,protein:3.4,carbs:5,fats:1}:p.includes("egg")?{calories:155,protein:13,carbs:1.1,fats:11}:p.includes("bread")?{calories:265,protein:9,carbs:49,fats:3.2}:{calories:200,protein:10,carbs:25,fats:8}},c=async()=>{if(!("webkitSpeechRecognition"in window)&&!("SpeechRecognition"in window)){dn.error("Speech recognition not supported in this browser");return}const x=window.webkitSpeechRecognition||window.SpeechRecognition,p=new x;p.continuous=!1,p.interimResults=!1,p.lang="en-US",p.onstart=()=>{l(!0)},p.onresult=async S=>{const m=S.results[0][0].transcript;l(!1);const v=await u(m);e({...v,method:"voice"}),n(!1),dn.success("Food added via voice!")},p.onerror=()=>{l(!1),dn.error("Voice recognition failed")},p.start()},y=async x=>{var m;if(!((m=x.target.files)==null?void 0:m[0]))return;dn.success("Image analysis feature coming soon!");const S=await u("Image of food");e({...S,name:"Food from image",method:"image"}),n(!1)};return g.jsxs(Kg,{open:t,onOpenChange:n,children:[g.jsx(Gg,{asChild:!0,children:g.jsxs($t,{className:"w-full",size:"lg",children:[g.jsx(Zx,{className:"w-5 h-5 mr-2"}),"Add Food"]})}),g.jsxs(Gc,{className:"sm:max-w-md",children:[g.jsxs(Yc,{children:[g.jsx(Xc,{children:"Add Food Entry"}),g.jsx(qc,{children:"Analyze your food using text, voice, or image input to track macros automatically."})]}),g.jsxs(Lk,{defaultValue:"text",className:"w-full",children:[g.jsxs(gy,{className:"grid w-full grid-cols-3",children:[g.jsxs(cs,{value:"text",className:"flex items-center gap-2",children:[g.jsx(uv,{className:"w-4 h-4"}),"Type"]}),g.jsxs(cs,{value:"voice",className:"flex items-center gap-2",children:[g.jsx(mu,{className:"w-4 h-4"}),"Voice"]}),g.jsxs(cs,{value:"image",className:"flex items-center gap-2",children:[g.jsx(lv,{className:"w-4 h-4"}),"Image"]})]}),g.jsxs(ds,{value:"text",className:"space-y-4",children:[g.jsxs("div",{className:"space-y-2",children:[g.jsx(Rr,{htmlFor:"food-description",children:"Describe your food"}),g.jsx(Xg,{id:"food-description",placeholder:"e.g., 1 cup of brown rice with grilled chicken breast",value:i,onChange:x=>s(x.target.value),rows:3})]}),g.jsxs($t,{onClick:f,className:"w-full",disabled:r||!i.trim(),children:[r&&g.jsx(Ef,{className:"w-4 h-4 mr-2 animate-spin"}),"Analyze Food"]})]}),g.jsx(ds,{value:"voice",className:"space-y-4",children:g.jsxs("div",{className:"text-center space-y-4",children:[g.jsx("div",{className:"text-sm text-muted-foreground",children:"Tap the button and describe your food"}),g.jsx($t,{onClick:c,className:"w-full",variant:a?"destructive":"default",disabled:r,children:a?g.jsxs(g.Fragment,{children:[g.jsx(Ef,{className:"w-4 h-4 mr-2 animate-spin"}),"Listening..."]}):g.jsxs(g.Fragment,{children:[g.jsx(mu,{className:"w-4 h-4 mr-2"}),"Start Voice Input"]})})]})}),g.jsx(ds,{value:"image",className:"space-y-4",children:g.jsxs("div",{className:"text-center space-y-4",children:[g.jsx("div",{className:"text-sm text-muted-foreground",children:"Upload a photo of your food"}),g.jsx(Tr,{type:"file",accept:"image/*",onChange:y,className:"cursor-pointer",disabled:r})]})})]})]})]})}function Uk({currentGoals:e,onUpdateGoals:t}){const[n,r]=h.useState(!1),[o,i]=h.useState(e),s=()=>{if(o.calories<=0||o.protein<=0||o.carbs<=0||o.fats<=0){dn.error("All values must be greater than 0");return}t(o),r(!1),dn.success("Daily goals updated!")},a=()=>{i(e)},u=(()=>{const d=o.protein*4,c=o.carbs*4,y=o.fats*9;return d+c+y})(),f=Math.abs(o.calories-u)>50;return g.jsxs(Kg,{open:n,onOpenChange:r,children:[g.jsx(Gg,{asChild:!0,children:g.jsxs($t,{variant:"outline",size:"sm",className:"gap-2",children:[g.jsx(Jx,{className:"w-4 h-4"}),"Edit Goals"]})}),g.jsxs(Gc,{className:"sm:max-w-md",children:[g.jsxs(Yc,{children:[g.jsx(Xc,{children:"Daily Macro Goals"}),g.jsx(qc,{children:"Customize your daily calorie and macro targets to match your fitness goals."})]}),g.jsxs("div",{className:"space-y-6",children:[g.jsxs("div",{className:"space-y-4",children:[g.jsxs("div",{className:"space-y-2",children:[g.jsx(Rr,{htmlFor:"calories",children:"Daily Calories"}),g.jsx(Tr,{id:"calories",type:"number",value:o.calories,onChange:d=>i(c=>({...c,calories:parseInt(d.target.value)||0})),placeholder:"2000",min:"1"})]}),g.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[g.jsxs("div",{className:"space-y-2",children:[g.jsx(Rr,{htmlFor:"protein",children:"Protein (g)"}),g.jsx(Tr,{id:"protein",type:"number",value:o.protein,onChange:d=>i(c=>({...c,protein:parseInt(d.target.value)||0})),placeholder:"150",min:"1",className:"text-sm"}),g.jsxs("div",{className:"text-xs text-protein",children:[o.protein*4," cal"]})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(Rr,{htmlFor:"carbs",children:"Carbs (g)"}),g.jsx(Tr,{id:"carbs",type:"number",value:o.carbs,onChange:d=>i(c=>({...c,carbs:parseInt(d.target.value)||0})),placeholder:"250",min:"1",className:"text-sm"}),g.jsxs("div",{className:"text-xs text-carbs",children:[o.carbs*4," cal"]})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(Rr,{htmlFor:"fats",children:"Fats (g)"}),g.jsx(Tr,{id:"fats",type:"number",value:o.fats,onChange:d=>i(c=>({...c,fats:parseInt(d.target.value)||0})),placeholder:"67",min:"1",className:"text-sm"}),g.jsxs("div",{className:"text-xs text-fats",children:[o.fats*9," cal"]})]})]})]}),f&&g.jsxs("div",{className:"p-3 bg-accent/50 rounded-lg border",children:[g.jsx("div",{className:"text-sm font-medium text-foreground",children:"Macro Balance Check"}),g.jsxs("div",{className:"text-xs text-muted-foreground mt-1",children:["Your macro calories (",u,") don't match your total calorie goal (",o.calories,"). Consider adjusting for better accuracy."]})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx("div",{className:"text-sm font-medium text-foreground",children:"Quick Presets"}),g.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[g.jsx($t,{variant:"outline",size:"sm",onClick:()=>i({calories:1800,protein:135,carbs:180,fats:60}),className:"text-xs",children:"Weight Loss"}),g.jsx($t,{variant:"outline",size:"sm",onClick:()=>i({calories:2500,protein:188,carbs:313,fats:83}),className:"text-xs",children:"Muscle Gain"})]})]}),g.jsxs("div",{className:"flex gap-2",children:[g.jsx($t,{variant:"outline",onClick:a,className:"flex-1",children:"Reset"}),g.jsx($t,{onClick:s,className:"flex-1",children:"Save Goals"})]})]})]})]})}const Bk=()=>{const[e,t]=h.useState([]),[n,r]=h.useState({calories:2e3,protein:150,carbs:250,fats:67});h.useEffect(()=>{const u=localStorage.getItem("macroTracker-entries");if(u){const d=JSON.parse(u).map(c=>({...c,timestamp:new Date(c.timestamp)}));t(d)}const f=localStorage.getItem("macroTracker-goals");f&&r(JSON.parse(f))},[]),h.useEffect(()=>{localStorage.setItem("macroTracker-entries",JSON.stringify(e))},[e]),h.useEffect(()=>{localStorage.setItem("macroTracker-goals",JSON.stringify(n))},[n]);const o=u=>{r(u)},i=u=>{const f={...u,id:Date.now().toString(),timestamp:new Date};t(d=>[f,...d])},s=new Date().toDateString(),a=e.filter(u=>u.timestamp.toDateString()===s),l=a.reduce((u,f)=>({calories:u.calories+f.macros.calories,protein:u.protein+f.macros.protein,carbs:u.carbs+f.macros.carbs,fats:u.fats+f.macros.fats}),{calories:0,protein:0,carbs:0,fats:0});return g.jsxs("div",{className:"min-h-screen bg-background",children:[g.jsx("div",{className:"sticky top-0 z-50 bg-background/80 backdrop-blur-md border-b",children:g.jsxs("div",{className:"container max-w-md mx-auto p-4",children:[g.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"MacroTracker"}),g.jsx("p",{className:"text-sm text-muted-foreground",children:"Track your daily nutrition"})]})}),g.jsxs("div",{className:"container max-w-md mx-auto p-4 space-y-6",children:[g.jsxs(Fo,{children:[g.jsx(Tu,{className:"pb-4",children:g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsxs(Ru,{className:"flex items-center gap-2",children:[g.jsx(e1,{className:"w-5 h-5 text-primary"}),"Today's Progress"]}),g.jsx(Uk,{currentGoals:n,onUpdateGoals:o})]})}),g.jsx(zo,{children:g.jsx(ab,{current:l,goals:n})})]}),g.jsx($k,{onAddFood:i}),g.jsxs(Fo,{children:[g.jsx(Tu,{className:"pb-4",children:g.jsxs(Ru,{className:"flex items-center gap-2",children:[g.jsx(qx,{className:"w-5 h-5 text-primary"}),"Recent Entries"]})}),g.jsx(zo,{className:"space-y-3",children:a.length===0?g.jsxs("div",{className:"text-center py-8",children:[g.jsx("p",{className:"text-muted-foreground",children:"No entries for today"}),g.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Add your first meal!"})]}):a.map(u=>g.jsx(cb,{entry:u},u.id))})]}),g.jsx(Fo,{children:g.jsx(zo,{className:"pt-6",children:g.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[g.jsxs("div",{children:[g.jsx("p",{className:"text-2xl font-bold text-primary",children:a.length}),g.jsx("p",{className:"text-sm text-muted-foreground",children:"Meals Today"})]}),g.jsxs("div",{children:[g.jsxs("p",{className:"text-2xl font-bold text-primary",children:[Math.round(l.calories/n.calories*100),"%"]}),g.jsx("p",{className:"text-sm text-muted-foreground",children:"Daily Goal"})]})]})})})]})]})},Vk=()=>{const e=ug();return h.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:g.jsxs("div",{className:"text-center",children:[g.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),g.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),g.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},Wk=new uC,Hk=()=>g.jsx(dC,{client:Wk,children:g.jsxs($E,{children:[g.jsx(D1,{}),g.jsx(dS,{}),g.jsx(YC,{children:g.jsxs(QC,{children:[g.jsx(Pu,{path:"/",element:g.jsx(Bk,{})}),g.jsx(Pu,{path:"*",element:g.jsx(Vk,{})})]})})]})});Am(document.getElementById("root")).render(g.jsx(Hk,{}));
