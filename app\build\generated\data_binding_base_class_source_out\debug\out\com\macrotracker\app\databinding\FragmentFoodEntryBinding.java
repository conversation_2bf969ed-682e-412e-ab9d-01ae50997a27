// Generated by view binder compiler. Do not edit!
package com.macrotracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.macrotracker.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentFoodEntryBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button buttonAddFood;

  @NonNull
  public final Button buttonImageEntry;

  @NonNull
  public final Button buttonManualEntry;

  @NonNull
  public final Button buttonSpeechEntry;

  @NonNull
  public final TextInputEditText editTextFood;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView textViewInstructions;

  @NonNull
  public final TextView textViewStatus;

  private FragmentFoodEntryBinding(@NonNull ScrollView rootView, @NonNull Button buttonAddFood,
      @NonNull Button buttonImageEntry, @NonNull Button buttonManualEntry,
      @NonNull Button buttonSpeechEntry, @NonNull TextInputEditText editTextFood,
      @NonNull ProgressBar progressBar, @NonNull TextView textViewInstructions,
      @NonNull TextView textViewStatus) {
    this.rootView = rootView;
    this.buttonAddFood = buttonAddFood;
    this.buttonImageEntry = buttonImageEntry;
    this.buttonManualEntry = buttonManualEntry;
    this.buttonSpeechEntry = buttonSpeechEntry;
    this.editTextFood = editTextFood;
    this.progressBar = progressBar;
    this.textViewInstructions = textViewInstructions;
    this.textViewStatus = textViewStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentFoodEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentFoodEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_food_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentFoodEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add_food;
      Button buttonAddFood = ViewBindings.findChildViewById(rootView, id);
      if (buttonAddFood == null) {
        break missingId;
      }

      id = R.id.button_image_entry;
      Button buttonImageEntry = ViewBindings.findChildViewById(rootView, id);
      if (buttonImageEntry == null) {
        break missingId;
      }

      id = R.id.button_manual_entry;
      Button buttonManualEntry = ViewBindings.findChildViewById(rootView, id);
      if (buttonManualEntry == null) {
        break missingId;
      }

      id = R.id.button_speech_entry;
      Button buttonSpeechEntry = ViewBindings.findChildViewById(rootView, id);
      if (buttonSpeechEntry == null) {
        break missingId;
      }

      id = R.id.edit_text_food;
      TextInputEditText editTextFood = ViewBindings.findChildViewById(rootView, id);
      if (editTextFood == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.text_view_instructions;
      TextView textViewInstructions = ViewBindings.findChildViewById(rootView, id);
      if (textViewInstructions == null) {
        break missingId;
      }

      id = R.id.text_view_status;
      TextView textViewStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewStatus == null) {
        break missingId;
      }

      return new FragmentFoodEntryBinding((ScrollView) rootView, buttonAddFood, buttonImageEntry,
          buttonManualEntry, buttonSpeechEntry, editTextFood, progressBar, textViewInstructions,
          textViewStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
