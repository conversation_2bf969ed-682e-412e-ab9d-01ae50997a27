package com.macrotracker.app.data.entity

data class DailySummary(
    val date: String, // Format: yyyy-MM-dd
    val totalCalories: Double = 0.0,
    val totalProtein: Double = 0.0,
    val totalCarbs: Double = 0.0,
    val totalFat: Double = 0.0,
    val totalFiber: Double = 0.0,
    val totalSugar: Double = 0.0,
    val totalSodium: Double = 0.0,
    val entryCount: Int = 0
) {
    fun getCaloriesProgress(goal: Double): Float = 
        if (goal > 0) (totalCalories / goal).toFloat().coerceAtMost(1f) else 0f
    
    fun getProteinProgress(goal: Double): Float = 
        if (goal > 0) (totalProtein / goal).toFloat().coerceAtMost(1f) else 0f
    
    fun getCarbsProgress(goal: Double): Float = 
        if (goal > 0) (totalCarbs / goal).toFloat().coerceAtMost(1f) else 0f
    
    fun getFatProgress(goal: Double): Float = 
        if (goal > 0) (totalFat / goal).toFloat().coerceAtMost(1f) else 0f
    
    fun getFiberProgress(goal: Double): Float = 
        if (goal > 0) (totalFiber / goal).toFloat().coerceAtMost(1f) else 0f
    
    fun getSugarProgress(goal: Double): Float = 
        if (goal > 0) (totalSugar / goal).toFloat().coerceAtMost(1f) else 0f
    
    fun getSodiumProgress(goal: Double): Float = 
        if (goal > 0) (totalSodium / goal).toFloat().coerceAtMost(1f) else 0f
}
