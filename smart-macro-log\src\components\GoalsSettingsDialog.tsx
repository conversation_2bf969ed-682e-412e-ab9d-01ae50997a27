import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Settings } from "lucide-react";
import { DailyGoals } from "@/types/nutrition";
import { toast } from "sonner";

interface GoalsSettingsDialogProps {
  currentGoals: DailyGoals;
  onUpdateGoals: (goals: DailyGoals) => void;
}

export function GoalsSettingsDialog({ currentGoals, onUpdateGoals }: GoalsSettingsDialogProps) {
  const [open, setOpen] = useState(false);
  const [goals, setGoals] = useState<DailyGoals>(currentGoals);

  const handleSave = () => {
    // Validate that all values are positive numbers
    if (goals.calories <= 0 || goals.protein <= 0 || goals.carbs <= 0 || goals.fats <= 0) {
      toast.error("All values must be greater than 0");
      return;
    }

    onUpdateGoals(goals);
    setOpen(false);
    toast.success("Daily goals updated!");
  };

  const handleReset = () => {
    setGoals(currentGoals);
  };

  const calculateMacroCalories = () => {
    const proteinCals = goals.protein * 4;
    const carbsCals = goals.carbs * 4;
    const fatsCals = goals.fats * 9;
    return proteinCals + carbsCals + fatsCals;
  };

  const macroCalories = calculateMacroCalories();
  const macroMismatch = Math.abs(goals.calories - macroCalories) > 50;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Settings className="w-4 h-4" />
          Edit Goals
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Daily Macro Goals</DialogTitle>
          <DialogDescription>
            Customize your daily calorie and macro targets to match your fitness goals.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="calories">Daily Calories</Label>
              <Input
                id="calories"
                type="number"
                value={goals.calories}
                onChange={(e) => setGoals(prev => ({ ...prev, calories: parseInt(e.target.value) || 0 }))}
                placeholder="2000"
                min="1"
              />
            </div>

            <div className="grid grid-cols-3 gap-3">
              <div className="space-y-2">
                <Label htmlFor="protein">Protein (g)</Label>
                <Input
                  id="protein"
                  type="number"
                  value={goals.protein}
                  onChange={(e) => setGoals(prev => ({ ...prev, protein: parseInt(e.target.value) || 0 }))}
                  placeholder="150"
                  min="1"
                  className="text-sm"
                />
                <div className="text-xs text-protein">
                  {goals.protein * 4} cal
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="carbs">Carbs (g)</Label>
                <Input
                  id="carbs"
                  type="number"
                  value={goals.carbs}
                  onChange={(e) => setGoals(prev => ({ ...prev, carbs: parseInt(e.target.value) || 0 }))}
                  placeholder="250"
                  min="1"
                  className="text-sm"
                />
                <div className="text-xs text-carbs">
                  {goals.carbs * 4} cal
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="fats">Fats (g)</Label>
                <Input
                  id="fats"
                  type="number"
                  value={goals.fats}
                  onChange={(e) => setGoals(prev => ({ ...prev, fats: parseInt(e.target.value) || 0 }))}
                  placeholder="67"
                  min="1"
                  className="text-sm"
                />
                <div className="text-xs text-fats">
                  {goals.fats * 9} cal
                </div>
              </div>
            </div>
          </div>

          {macroMismatch && (
            <div className="p-3 bg-accent/50 rounded-lg border">
              <div className="text-sm font-medium text-foreground">Macro Balance Check</div>
              <div className="text-xs text-muted-foreground mt-1">
                Your macro calories ({macroCalories}) don't match your total calorie goal ({goals.calories}).
                Consider adjusting for better accuracy.
              </div>
            </div>
          )}

          <div className="space-y-2">
            <div className="text-sm font-medium text-foreground">Quick Presets</div>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setGoals({ calories: 1800, protein: 135, carbs: 180, fats: 60 })}
                className="text-xs"
              >
                Weight Loss
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setGoals({ calories: 2500, protein: 188, carbs: 313, fats: 83 })}
                className="text-xs"
              >
                Muscle Gain
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleReset} className="flex-1">
              Reset
            </Button>
            <Button onClick={handleSave} className="flex-1">
              Save Goals
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}