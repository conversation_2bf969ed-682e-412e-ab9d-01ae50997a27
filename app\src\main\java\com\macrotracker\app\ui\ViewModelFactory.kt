package com.macrotracker.app.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.macrotracker.app.data.repository.MacroRepository
import com.macrotracker.app.service.FoodAnalysisService
import com.macrotracker.app.ui.dashboard.DashboardViewModel
import com.macrotracker.app.ui.foodentry.FoodEntryViewModel
import com.macrotracker.app.ui.history.HistoryViewModel
import com.macrotracker.app.ui.settings.SettingsViewModel

class ViewModelFactory(
    private val repository: MacroRepository,
    private val foodAnalysisService: FoodAnalysisService
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when {
            modelClass.isAssignableFrom(DashboardViewModel::class.java) -> {
                DashboardViewModel(repository) as T
            }
            modelClass.isAssignableFrom(FoodEntryViewModel::class.java) -> {
                FoodEntryViewModel(repository, foodAnalysisService) as T
            }
            modelClass.isAssignableFrom(HistoryViewModel::class.java) -> {
                HistoryViewModel(repository) as T
            }
            modelClass.isAssignableFrom(SettingsViewModel::class.java) -> {
                SettingsViewModel(repository) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
