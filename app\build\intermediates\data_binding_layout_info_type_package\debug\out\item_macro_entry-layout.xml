<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_macro_entry" modulePackage="com.macrotracker.app" filePath="app\src\main\res\layout\item_macro_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_macro_entry_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="162" endOffset="51"/></Target><Target id="@+id/text_food_name" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="28" endOffset="41"/></Target><Target id="@+id/text_entry_method" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="39" endOffset="37"/></Target><Target id="@+id/text_timestamp" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="50" endOffset="41"/></Target><Target id="@+id/text_calories" view="TextView"><Expressions/><location startLine="65" startOffset="16" endLine="72" endOffset="38"/></Target><Target id="@+id/text_protein" view="TextView"><Expressions/><location startLine="90" startOffset="16" endLine="97" endOffset="38"/></Target><Target id="@+id/text_carbs" view="TextView"><Expressions/><location startLine="115" startOffset="16" endLine="122" endOffset="37"/></Target><Target id="@+id/text_fat" view="TextView"><Expressions/><location startLine="140" startOffset="16" endLine="147" endOffset="38"/></Target></Targets></Layout>