package com.macrotracker.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "macro_entries")
data class MacroEntry(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val foodName: String,
    val quantity: String,
    val calories: Double,
    val protein: Double, // in grams
    val carbs: Double, // in grams
    val fat: Double, // in grams
    val fiber: Double = 0.0, // in grams
    val sugar: Double = 0.0, // in grams
    val sodium: Double = 0.0, // in milligrams
    val timestamp: Date = Date(),
    val entryMethod: EntryMethod = EntryMethod.MANUAL, // How the food was entered
    val imageUri: String? = null // URI of the food image if taken
)

enum class EntryMethod {
    MANUAL,
    SPEECH,
    IMAGE
}
