<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\src\main\res"><file name="bg_entry_method" path="C:\MyCal\app\src\main\res\drawable\bg_entry_method.xml" qualifiers="" type="drawable"/><file name="ic_add_food" path="C:\MyCal\app\src\main\res\drawable\ic_add_food.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\MyCal\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_dashboard" path="C:\MyCal\app\src\main\res\drawable\ic_dashboard.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\MyCal\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_history" path="C:\MyCal\app\src\main\res\drawable\ic_history.xml" qualifiers="" type="drawable"/><file name="ic_mic" path="C:\MyCal\app\src\main\res\drawable\ic_mic.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\MyCal\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\MyCal\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_dashboard" path="C:\MyCal\app\src\main\res\layout\fragment_dashboard.xml" qualifiers="" type="layout"/><file name="fragment_food_entry" path="C:\MyCal\app\src\main\res\layout\fragment_food_entry.xml" qualifiers="" type="layout"/><file name="fragment_history" path="C:\MyCal\app\src\main\res\layout\fragment_history.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="C:\MyCal\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="item_macro_entry" path="C:\MyCal\app\src\main\res\layout\item_macro_entry.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="C:\MyCal\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file path="C:\MyCal\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#FF6200EE</color><color name="primary_variant">#FF3700B3</color><color name="secondary">#FF03DAC5</color><color name="secondary_variant">#FF018786</color><color name="calories_color">#FF9C27B0</color><color name="protein_color">#FF2196F3</color><color name="carbs_color">#FF4CAF50</color><color name="fat_color">#FFFF9800</color><color name="fiber_color">#FF795548</color><color name="sugar_color">#FFE91E63</color><color name="sodium_color">#FF607D8B</color><color name="background">#FFFFFFFF</color><color name="surface">#FFFFFFFF</color><color name="error">#FFCF6679</color><color name="on_primary">#FFFFFFFF</color><color name="on_secondary">#FF000000</color><color name="on_background">#FF000000</color><color name="on_surface">#FF000000</color><color name="on_error">#FF000000</color><color name="progress_background">#FFE0E0E0</color><color name="progress_low">#FF4CAF50</color><color name="progress_medium">#FFFF9800</color><color name="progress_high">#FFF44336</color></file><file path="C:\MyCal\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Macro Tracker</string><string name="nav_dashboard">Dashboard</string><string name="nav_add_food">Add Food</string><string name="nav_history">History</string><string name="nav_settings">Settings</string><string name="enter_food_manually">Enter food manually</string><string name="speak_food">Speak food</string><string name="take_photo">Take photo</string><string name="food_name_hint">Enter food name (e.g., "1 cup rice")</string><string name="add_food">Add Food</string><string name="analyzing_food">Analyzing food...</string><string name="listening">Listening...</string><string name="daily_macros">Daily Macros</string><string name="calories">Calories</string><string name="protein">Protein</string><string name="carbs">Carbs</string><string name="fat">Fat</string><string name="fiber">Fiber</string><string name="sugar">Sugar</string><string name="sodium">Sodium</string><string name="kcal">kcal</string><string name="grams">g</string><string name="milligrams">mg</string><string name="camera_permission_required">Camera permission is required to take photos</string><string name="microphone_permission_required">Microphone permission is required for speech recognition</string><string name="grant_permission">Grant Permission</string><string name="error_network">Network error. Please check your connection.</string><string name="error_api">Error analyzing food. Please try again.</string><string name="error_speech_recognition">Speech recognition failed. Please try again.</string><string name="error_camera">Camera error. Please try again.</string><string name="daily_goals">Daily Goals</string><string name="set_goals">Set Goals</string><string name="calories_goal">Calories Goal</string><string name="protein_goal">Protein Goal</string><string name="carbs_goal">Carbs Goal</string><string name="fat_goal">Fat Goal</string></file><file path="C:\MyCal\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.MacroTracker" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorOnError">@color/on_error</item>
    </style><style name="Theme.MacroTracker" parent="Base.Theme.MacroTracker"/><style name="MacroButton" parent="Widget.Material3.Button">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">8dp</item>
    </style><style name="MacroButtonOutlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">8dp</item>
    </style><style name="MacroCard" parent="Widget.Material3.CardView.Elevated">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
    </style></file><file name="backup_rules" path="C:\MyCal\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\MyCal\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\MyCal\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>