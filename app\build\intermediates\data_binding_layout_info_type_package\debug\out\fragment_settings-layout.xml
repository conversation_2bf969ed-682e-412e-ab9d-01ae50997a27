<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="com.macrotracker.app" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="12"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="29" startOffset="8" endLine="34" endOffset="39"/></Target><Target id="@+id/edit_text_calories_goal" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="12" endLine="49" endOffset="35"/></Target><Target id="@+id/edit_text_protein_goal" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="61" startOffset="12" endLine="66" endOffset="34"/></Target><Target id="@+id/edit_text_carbs_goal" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="78" startOffset="12" endLine="83" endOffset="34"/></Target><Target id="@+id/edit_text_fat_goal" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="95" startOffset="12" endLine="100" endOffset="33"/></Target><Target id="@+id/button_save_goals" view="Button"><Expressions/><location startLine="104" startOffset="8" endLine="109" endOffset="39"/></Target></Targets></Layout>