package com.macrotracker.app.ui.foodentry

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.macrotracker.app.data.entity.EntryMethod
import com.macrotracker.app.data.entity.MacroEntry
import com.macrotracker.app.data.repository.MacroRepository
import com.macrotracker.app.service.FoodAnalysisService
import kotlinx.coroutines.launch
class FoodEntryViewModel(
    private val repository: MacroRepository,
    private val foodAnalysisService: FoodAnalysisService
) : ViewModel() {
    
    private val _entryMethod = MutableLiveData<EntryMethod>()
    val entryMethod: LiveData<EntryMethod> = _entryMethod
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage
    
    private val _analyzedEntry = MutableLiveData<MacroEntry?>()
    val analyzedEntry: LiveData<MacroEntry?> = _analyzedEntry
    
    fun setEntryMethod(method: EntryMethod) {
        _entryMethod.value = method
        _errorMessage.value = null
    }
    
    fun analyzeFoodText(foodDescription: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = foodAnalysisService.analyzeFoodText(foodDescription)
                
                result.onSuccess { macroEntry ->
                    _analyzedEntry.value = macroEntry
                    saveMacroEntry(macroEntry)
                }.onFailure { exception ->
                    _errorMessage.value = "Failed to analyze food: ${exception.message}"
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "Error analyzing food: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun analyzeFoodFromSpeech(speechText: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = foodAnalysisService.analyzeFoodText(speechText)
                
                result.onSuccess { macroEntry ->
                    val speechEntry = macroEntry.copy(entryMethod = EntryMethod.SPEECH)
                    _analyzedEntry.value = speechEntry
                    saveMacroEntry(speechEntry)
                }.onFailure { exception ->
                    _errorMessage.value = "Failed to analyze speech: ${exception.message}"
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "Error analyzing speech: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun analyzeFoodFromImage(bitmap: android.graphics.Bitmap, description: String = "") {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = foodAnalysisService.analyzeFoodImage(bitmap, description)
                
                result.onSuccess { macroEntry ->
                    _analyzedEntry.value = macroEntry
                    saveMacroEntry(macroEntry)
                }.onFailure { exception ->
                    _errorMessage.value = "Failed to analyze image: ${exception.message}"
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "Error analyzing image: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    private suspend fun saveMacroEntry(macroEntry: MacroEntry) {
        try {
            repository.insertEntry(macroEntry)
            _successMessage.value = "Food entry saved successfully!"
        } catch (e: Exception) {
            _errorMessage.value = "Failed to save entry: ${e.message}"
        }
    }
    
    fun clearMessages() {
        _errorMessage.value = null
        _successMessage.value = null
    }
}
