package com.macrotracker.app.service

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class CameraService(private val context: Context) {
    
    private var imageCapture: ImageCapture? = null
    private var cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    
    val capturedImageLiveData = MutableLiveData<Bitmap>()
    val errorLiveData = MutableLiveData<String>()
    
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner
    ) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        
        cameraProviderFuture.addListener({
            val cameraProvider: ProcessCameraProvider = cameraProviderFuture.get()
            
            // Preview
            val preview = Preview.Builder()
                .build()
                .also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }
            
            // ImageCapture
            imageCapture = ImageCapture.Builder()
                .build()
            
            // Select back camera as a default
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            
            try {
                // Unbind use cases before rebinding
                cameraProvider.unbindAll()
                
                // Bind use cases to camera
                cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageCapture
                )
                
            } catch (exc: Exception) {
                errorLiveData.value = "Camera initialization failed: ${exc.message}"
            }
            
        }, ContextCompat.getMainExecutor(context))
    }
    
    fun takePhoto() {
        val imageCapture = imageCapture ?: return
        
        // Create time stamped name and MediaStore entry
        val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
            .format(System.currentTimeMillis())
        
        // Create output file to hold the image
        val photoFile = File(
            context.getExternalFilesDir(null),
            "$name.jpg"
        )
        
        // Create output options object which contains file + metadata
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile)
            .build()
        
        // Set up image capture listener, which is triggered after photo has been taken
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exception: ImageCaptureException) {
                    errorLiveData.value = "Photo capture failed: ${exception.message}"
                }
                
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    // Convert saved image to bitmap
                    try {
                        val bitmap = BitmapFactory.decodeFile(photoFile.absolutePath)
                        val rotatedBitmap = rotateBitmapIfNeeded(bitmap)
                        capturedImageLiveData.value = rotatedBitmap
                        
                        // Clean up the temporary file
                        photoFile.delete()
                    } catch (e: Exception) {
                        errorLiveData.value = "Failed to process captured image: ${e.message}"
                    }
                }
            }
        )
    }
    
    private fun rotateBitmapIfNeeded(bitmap: Bitmap): Bitmap {
        // For simplicity, we'll assume the image is correctly oriented
        // In a production app, you might want to check EXIF data for orientation
        return bitmap
    }
    
    fun shutdown() {
        cameraExecutor.shutdown()
    }
}
