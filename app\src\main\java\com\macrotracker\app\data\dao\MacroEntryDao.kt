package com.macrotracker.app.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.macrotracker.app.data.entity.MacroEntry
import java.util.Date

@Dao
interface MacroEntryDao {
    
    @Query("SELECT * FROM macro_entries ORDER BY timestamp DESC")
    fun getAllEntries(): LiveData<List<MacroEntry>>
    
    @Query("SELECT * FROM macro_entries WHERE date(timestamp/1000, 'unixepoch') = date(:date/1000, 'unixepoch') ORDER BY timestamp DESC")
    fun getEntriesForDate(date: Date): LiveData<List<MacroEntry>>
    
    @Query("SELECT * FROM macro_entries WHERE date(timestamp/1000, 'unixepoch') = date(:date/1000, 'unixepoch') ORDER BY timestamp DESC")
    suspend fun getEntriesForDateSync(date: Date): List<MacroEntry>
    
    @Query("""
        SELECT 
            COALESCE(SUM(calories), 0) as totalCalories,
            COALESCE(SUM(protein), 0) as totalProtein,
            COALESCE(SUM(carbs), 0) as totalCarbs,
            COALESCE(SUM(fat), 0) as totalFat,
            COALESCE(SUM(fiber), 0) as totalFiber,
            COALESCE(SUM(sugar), 0) as totalSugar,
            COALESCE(SUM(sodium), 0) as totalSodium,
            COUNT(*) as entryCount
        FROM macro_entries 
        WHERE date(timestamp/1000, 'unixepoch') = date(:date/1000, 'unixepoch')
    """)
    suspend fun getDailySummary(date: Date): DailySummaryResult
    
    @Query("SELECT * FROM macro_entries WHERE id = :id")
    suspend fun getEntryById(id: Long): MacroEntry?
    
    @Insert
    suspend fun insertEntry(entry: MacroEntry): Long
    
    @Update
    suspend fun updateEntry(entry: MacroEntry)
    
    @Delete
    suspend fun deleteEntry(entry: MacroEntry)
    
    @Query("DELETE FROM macro_entries WHERE id = :id")
    suspend fun deleteEntryById(id: Long)
    
    @Query("SELECT * FROM macro_entries WHERE date(timestamp/1000, 'unixepoch') BETWEEN date(:startDate/1000, 'unixepoch') AND date(:endDate/1000, 'unixepoch') ORDER BY timestamp DESC")
    fun getEntriesInDateRange(startDate: Date, endDate: Date): LiveData<List<MacroEntry>>
}

data class DailySummaryResult(
    val totalCalories: Double,
    val totalProtein: Double,
    val totalCarbs: Double,
    val totalFat: Double,
    val totalFiber: Double,
    val totalSugar: Double,
    val totalSodium: Double,
    val entryCount: Int
)
