package com.macrotracker.app.ui.foodentry

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.snackbar.Snackbar
import com.macrotracker.app.R
import com.macrotracker.app.databinding.FragmentFoodEntryBinding
import com.macrotracker.app.MacroTrackerApplication
import com.macrotracker.app.data.repository.MacroRepository
import com.macrotracker.app.service.FoodAnalysisService
import com.macrotracker.app.service.SpeechRecognitionService
import com.macrotracker.app.service.CameraService
import com.macrotracker.app.ui.ViewModelFactory

class FoodEntryFragment : Fragment() {

    private var _binding: FragmentFoodEntryBinding? = null
    private val binding get() = _binding!!

    private val viewModel: FoodEntryViewModel by viewModels {
        val app = requireActivity().application as MacroTrackerApplication
        val repository = MacroRepository(
            app.database.macroEntryDao(),
            app.database.dailyGoalsDao()
        )
        val foodAnalysisService = FoodAnalysisService()
        ViewModelFactory(repository, foodAnalysisService)
    }
    private lateinit var speechRecognitionService: SpeechRecognitionService
    private lateinit var cameraService: CameraService

    private val requestMicrophonePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startSpeechRecognition()
        } else {
            showError(getString(R.string.microphone_permission_required))
        }
    }

    private val requestCameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startImageCapture()
        } else {
            showError(getString(R.string.camera_permission_required))
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentFoodEntryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        speechRecognitionService = SpeechRecognitionService(requireContext())
        cameraService = CameraService(requireContext())
        setupUI()
        setupObservers()
        setupSpeechRecognition()
        setupCameraService()
    }
    
    private fun setupUI() {
        binding.buttonManualEntry.setOnClickListener {
            // Handle manual text entry
            viewModel.setEntryMethod(com.macrotracker.app.data.entity.EntryMethod.MANUAL)
            showManualEntryUI()
        }
        
        binding.buttonSpeechEntry.setOnClickListener {
            // Handle speech entry
            viewModel.setEntryMethod(com.macrotracker.app.data.entity.EntryMethod.SPEECH)
            startSpeechRecognition()
        }
        
        binding.buttonImageEntry.setOnClickListener {
            // Handle image entry
            viewModel.setEntryMethod(com.macrotracker.app.data.entity.EntryMethod.IMAGE)
            startImageCapture()
        }
        
        binding.buttonAddFood.setOnClickListener {
            val foodText = binding.editTextFood.text.toString()
            if (foodText.isNotBlank()) {
                viewModel.analyzeFoodText(foodText)
            }
        }
    }
    
    private fun setupObservers() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.buttonAddFood.isEnabled = !isLoading
        }
        
        viewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            error?.let {
                showError(it)
            }
        }

        viewModel.successMessage.observe(viewLifecycleOwner) { success ->
            success?.let {
                showSuccess(it)
                // Reset UI after successful entry
                binding.editTextFood.text?.clear()
                binding.textViewInstructions.text = "Choose how you want to add food:"
            }
        }
        
        viewModel.entryMethod.observe(viewLifecycleOwner) { method ->
            updateUIForEntryMethod(method)
        }
    }
    
    private fun showManualEntryUI() {
        binding.editTextFood.visibility = View.VISIBLE
        binding.buttonAddFood.visibility = View.VISIBLE
        binding.textViewInstructions.text = "Enter food description (e.g., '1 cup rice', '2 slices bread')"
    }
    
    private fun startSpeechRecognition() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestMicrophonePermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
        } else {
            speechRecognitionService.startListening()
        }
    }

    private fun setupSpeechRecognition() {
        speechRecognitionService.isListeningLiveData.observe(viewLifecycleOwner) { isListening ->
            if (isListening) {
                binding.textViewInstructions.text = getString(R.string.listening)
                binding.textViewStatus.visibility = View.VISIBLE
                binding.textViewStatus.text = getString(R.string.listening)
            } else {
                binding.textViewStatus.visibility = View.GONE
            }
        }

        speechRecognitionService.speechResultLiveData.observe(viewLifecycleOwner) { speechText ->
            binding.textViewInstructions.text = "Speech recognized: $speechText"
            viewModel.analyzeFoodFromSpeech(speechText)
        }

        speechRecognitionService.errorLiveData.observe(viewLifecycleOwner) { error ->
            showError(error)
        }
    }

    private fun showError(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG).show()
    }

    private fun showSuccess(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
    }
    
    private fun startImageCapture() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestCameraPermissionLauncher.launch(Manifest.permission.CAMERA)
        } else {
            // For simplicity, we'll show a camera dialog
            // In a full implementation, you might want to navigate to a camera fragment
            showCameraDialog()
        }
    }

    private fun showCameraDialog() {
        // This is a simplified implementation
        // In a real app, you might want to create a dedicated camera fragment
        binding.textViewInstructions.text = "Camera functionality ready. Take a photo to analyze food."
        // For now, we'll simulate taking a photo
        // You would implement actual camera preview and capture here
    }

    private fun setupCameraService() {
        cameraService.capturedImageLiveData.observe(viewLifecycleOwner) { bitmap ->
            binding.textViewInstructions.text = "Analyzing captured image..."
            viewModel.analyzeFoodFromImage(bitmap)
        }

        cameraService.errorLiveData.observe(viewLifecycleOwner) { error ->
            showError(error)
        }
    }
    
    private fun updateUIForEntryMethod(method: com.macrotracker.app.data.entity.EntryMethod) {
        when (method) {
            com.macrotracker.app.data.entity.EntryMethod.MANUAL -> {
                binding.editTextFood.visibility = View.VISIBLE
                binding.buttonAddFood.visibility = View.VISIBLE
            }
            com.macrotracker.app.data.entity.EntryMethod.SPEECH -> {
                binding.editTextFood.visibility = View.GONE
                binding.buttonAddFood.visibility = View.GONE
            }
            com.macrotracker.app.data.entity.EntryMethod.IMAGE -> {
                binding.editTextFood.visibility = View.GONE
                binding.buttonAddFood.visibility = View.GONE
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        speechRecognitionService.destroy()
        cameraService.shutdown()
        _binding = null
    }
}
