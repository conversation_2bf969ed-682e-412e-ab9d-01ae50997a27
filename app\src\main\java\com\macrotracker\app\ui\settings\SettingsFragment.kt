package com.macrotracker.app.ui.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.macrotracker.app.MacroTrackerApplication
import com.macrotracker.app.data.repository.MacroRepository
import com.macrotracker.app.databinding.FragmentSettingsBinding
import com.macrotracker.app.service.FoodAnalysisService
import com.macrotracker.app.ui.ViewModelFactory

class SettingsFragment : Fragment() {
    
    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: SettingsViewModel by viewModels {
        val app = requireActivity().application as MacroTrackerApplication
        val repository = MacroRepository(
            app.database.macroEntryDao(),
            app.database.dailyGoalsDao()
        )
        val foodAnalysisService = FoodAnalysisService()
        ViewModelFactory(repository, foodAnalysisService)
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        setupObservers()
    }
    
    private fun setupUI() {
        binding.buttonSaveGoals.setOnClickListener {
            saveGoals()
        }
    }
    
    private fun setupObservers() {
        viewModel.currentGoals.observe(viewLifecycleOwner) { goals ->
            goals?.let {
                binding.editTextCaloriesGoal.setText(it.caloriesGoal.toInt().toString())
                binding.editTextProteinGoal.setText(it.proteinGoal.toInt().toString())
                binding.editTextCarbsGoal.setText(it.carbsGoal.toInt().toString())
                binding.editTextFatGoal.setText(it.fatGoal.toInt().toString())
            }
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.buttonSaveGoals.isEnabled = !isLoading
        }
    }
    
    private fun saveGoals() {
        val calories = binding.editTextCaloriesGoal.text.toString().toDoubleOrNull() ?: 2000.0
        val protein = binding.editTextProteinGoal.text.toString().toDoubleOrNull() ?: 150.0
        val carbs = binding.editTextCarbsGoal.text.toString().toDoubleOrNull() ?: 250.0
        val fat = binding.editTextFatGoal.text.toString().toDoubleOrNull() ?: 65.0
        
        viewModel.saveGoals(calories, protein, carbs, fat)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
