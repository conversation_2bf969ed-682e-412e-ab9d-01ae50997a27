import { Progress } from "@/components/ui/progress";
import { MacroData, DailyGoals } from "@/types/nutrition";

interface MacroProgressProps {
  current: MacroData;
  goals: DailyGoals;
}

export function MacroProgress({ current, goals }: MacroProgressProps) {
  const macros = [
    { name: 'Calories', current: current.calories, goal: goals.calories, color: 'calories', unit: 'cal' },
    { name: 'Protein', current: current.protein, goal: goals.protein, color: 'protein', unit: 'g' },
    { name: 'Carbs', current: current.carbs, goal: goals.carbs, color: 'carbs', unit: 'g' },
    { name: 'Fats', current: current.fats, goal: goals.fats, color: 'fats', unit: 'g' },
  ];

  return (
    <div className="space-y-6">
      {macros.map((macro) => {
        const percentage = Math.min((macro.current / macro.goal) * 100, 100);
        return (
          <div key={macro.name} className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="font-medium text-foreground">{macro.name}</span>
              <span className="text-sm text-muted-foreground">
                {Math.round(macro.current)}/{macro.goal}{macro.unit}
              </span>
            </div>
            <div className="relative">
              <Progress
                value={percentage}
                className="h-3"
                style={{
                  background: `hsl(var(--${macro.color}) / 0.2)`,
                }}
              />
              <div
                className="absolute top-0 left-0 h-3 rounded-full transition-all duration-500"
                style={{
                  width: `${percentage}%`,
                  background: `hsl(var(--${macro.color}))`,
                }}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
}