<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/nav_add_food"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/text_view_instructions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Choose how you want to add food:"
            android:textSize="16sp"
            android:layout_marginBottom="24dp" />

        <!-- Entry Method Buttons -->
        <Button
            android:id="@+id/button_manual_entry"
            style="@style/MacroButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/enter_food_manually"
            android:drawableStart="@drawable/ic_edit"
            android:drawablePadding="8dp" />

        <Button
            android:id="@+id/button_speech_entry"
            style="@style/MacroButtonOutlined"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/speak_food"
            android:drawableStart="@drawable/ic_mic"
            android:drawablePadding="8dp" />

        <Button
            android:id="@+id/button_image_entry"
            style="@style/MacroButtonOutlined"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/take_photo"
            android:drawableStart="@drawable/ic_camera"
            android:drawablePadding="8dp" />

        <!-- Manual Entry UI -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:hint="@string/food_name_hint"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_food"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:inputType="textCapSentences|textMultiLine"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/button_add_food"
            style="@style/MacroButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/add_food"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/text_view_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textAlignment="center"
            android:visibility="gone"
            tools:text="Analyzing food..." />

    </LinearLayout>

</ScrollView>
