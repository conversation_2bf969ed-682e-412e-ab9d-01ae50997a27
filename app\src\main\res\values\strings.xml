<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Macro Tracker</string>
    
    <!-- Navigation -->
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_add_food">Add Food</string>
    <string name="nav_history">History</string>
    <string name="nav_settings">Settings</string>
    
    <!-- Food Entry -->
    <string name="enter_food_manually">Enter food manually</string>
    <string name="speak_food">Speak food</string>
    <string name="take_photo">Take photo</string>
    <string name="food_name_hint">Enter food name (e.g., "1 cup rice")</string>
    <string name="add_food">Add Food</string>
    <string name="analyzing_food">Analyzing food...</string>
    <string name="listening">Listening...</string>
    
    <!-- Dashboard -->
    <string name="daily_macros">Daily Macros</string>
    <string name="calories">Calories</string>
    <string name="protein">Protein</string>
    <string name="carbs">Carbs</string>
    <string name="fat">Fat</string>
    <string name="fiber">Fiber</string>
    <string name="sugar">Sugar</string>
    <string name="sodium">Sodium</string>
    
    <!-- Units -->
    <string name="kcal">kcal</string>
    <string name="grams">g</string>
    <string name="milligrams">mg</string>
    
    <!-- Permissions -->
    <string name="camera_permission_required">Camera permission is required to take photos</string>
    <string name="microphone_permission_required">Microphone permission is required for speech recognition</string>
    <string name="grant_permission">Grant Permission</string>
    
    <!-- Errors -->
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_api">Error analyzing food. Please try again.</string>
    <string name="error_speech_recognition">Speech recognition failed. Please try again.</string>
    <string name="error_camera">Camera error. Please try again.</string>
    
    <!-- Goals -->
    <string name="daily_goals">Daily Goals</string>
    <string name="set_goals">Set Goals</string>
    <string name="calories_goal">Calories Goal</string>
    <string name="protein_goal">Protein Goal</string>
    <string name="carbs_goal">Carbs Goal</string>
    <string name="fat_goal">Fat Goal</string>
</resources>
