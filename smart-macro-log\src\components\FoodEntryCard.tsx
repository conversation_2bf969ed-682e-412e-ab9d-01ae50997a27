import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FoodEntry } from "@/types/nutrition";
import { Mic, Camera, Type } from "lucide-react";

interface FoodEntryCardProps {
  entry: FoodEntry;
}

export function FoodEntryCard({ entry }: FoodEntryCardProps) {
  const getMethodIcon = (method: FoodEntry['method']) => {
    switch (method) {
      case 'voice':
        return <Mic className="w-3 h-3" />;
      case 'image':
        return <Camera className="w-3 h-3" />;
      case 'text':
        return <Type className="w-3 h-3" />;
    }
  };

  const getMethodColor = (method: FoodEntry['method']) => {
    switch (method) {
      case 'voice':
        return 'bg-protein/10 text-protein';
      case 'image':
        return 'bg-carbs/10 text-carbs';
      case 'text':
        return 'bg-fats/10 text-fats';
    }
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h3 className="font-semibold text-foreground">{entry.name}</h3>
            <p className="text-sm text-muted-foreground">{entry.quantity}</p>
          </div>
          <Badge variant="secondary" className={`${getMethodColor(entry.method)} flex items-center gap-1`}>
            {getMethodIcon(entry.method)}
            {entry.method}
          </Badge>
        </div>
        
        <div className="grid grid-cols-4 gap-2 text-center">
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Cal</p>
            <p className="font-semibold text-calories">{Math.round(entry.macros.calories)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Protein</p>
            <p className="font-semibold text-protein">{Math.round(entry.macros.protein)}g</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Carbs</p>
            <p className="font-semibold text-carbs">{Math.round(entry.macros.carbs)}g</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Fats</p>
            <p className="font-semibold text-fats">{Math.round(entry.macros.fats)}g</p>
          </div>
        </div>
        
        <p className="text-xs text-muted-foreground mt-3">
          {new Date(entry.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </p>
      </CardContent>
    </Card>
  );
}