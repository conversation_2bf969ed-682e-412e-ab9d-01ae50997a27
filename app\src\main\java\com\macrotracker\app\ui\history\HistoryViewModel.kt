package com.macrotracker.app.ui.history

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.macrotracker.app.data.entity.MacroEntry
import com.macrotracker.app.data.repository.MacroRepository
import kotlinx.coroutines.launch
class HistoryViewModel(
    private val repository: MacroRepository
) : ViewModel() {
    
    private val _entries = MutableLiveData<List<MacroEntry>>()
    val entries: LiveData<List<MacroEntry>> = _entries
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _selectedEntry = MutableLiveData<MacroEntry?>()
    val selectedEntry: LiveData<MacroEntry?> = _selectedEntry
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    init {
        loadEntries()
    }
    
    private fun loadEntries() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                // Observe all entries
                repository.getAllEntries().observeForever { entryList ->
                    _entries.value = entryList
                    _isLoading.value = false
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load entries: ${e.message}"
                _isLoading.value = false
            }
        }
    }
    
    fun selectEntry(entry: MacroEntry) {
        _selectedEntry.value = entry
    }
    
    fun deleteEntry(entry: MacroEntry) {
        viewModelScope.launch {
            try {
                repository.deleteEntry(entry)
            } catch (e: Exception) {
                _errorMessage.value = "Failed to delete entry: ${e.message}"
            }
        }
    }
    
    fun refreshEntries() {
        loadEntries()
    }
}
