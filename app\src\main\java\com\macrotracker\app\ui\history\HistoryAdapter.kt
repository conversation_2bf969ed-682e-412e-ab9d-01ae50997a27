package com.macrotracker.app.ui.history

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.macrotracker.app.data.entity.MacroEntry
import com.macrotracker.app.databinding.ItemMacroEntryBinding
import java.text.SimpleDateFormat
import java.util.*

class HistoryAdapter(
    private val onItemClick: (MacroEntry) -> Unit
) : ListAdapter<MacroEntry, HistoryAdapter.MacroEntryViewHolder>(MacroEntryDiffCallback()) {
    
    private val dateFormat = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
    private val todayFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    private val calendar = Calendar.getInstance()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MacroEntryViewHolder {
        val binding = ItemMacroEntryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MacroEntryViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: MacroEntryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class MacroEntryViewHolder(
        private val binding: ItemMacroEntryBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(entry: MacroEntry) {
            binding.apply {
                textFoodName.text = entry.foodName
                textEntryMethod.text = entry.entryMethod.name
                textTimestamp.text = formatTimestamp(entry.timestamp)
                
                textCalories.text = entry.calories.toInt().toString()
                textProtein.text = String.format("%.1f", entry.protein)
                textCarbs.text = String.format("%.1f", entry.carbs)
                textFat.text = String.format("%.1f", entry.fat)
                
                root.setOnClickListener {
                    onItemClick(entry)
                }
            }
        }
        
        private fun formatTimestamp(timestamp: Date): String {
            calendar.time = Date()
            val today = calendar.get(Calendar.DAY_OF_YEAR)
            val todayYear = calendar.get(Calendar.YEAR)
            
            calendar.time = timestamp
            val entryDay = calendar.get(Calendar.DAY_OF_YEAR)
            val entryYear = calendar.get(Calendar.YEAR)
            
            return if (today == entryDay && todayYear == entryYear) {
                "Today, ${todayFormat.format(timestamp)}"
            } else {
                dateFormat.format(timestamp)
            }
        }
    }
}

class MacroEntryDiffCallback : DiffUtil.ItemCallback<MacroEntry>() {
    override fun areItemsTheSame(oldItem: MacroEntry, newItem: MacroEntry): Boolean {
        return oldItem.id == newItem.id
    }
    
    override fun areContentsTheSame(oldItem: MacroEntry, newItem: MacroEntry): Boolean {
        return oldItem == newItem
    }
}
