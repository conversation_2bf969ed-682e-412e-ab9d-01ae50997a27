// Generated by view binder compiler. Do not edit!
package com.macrotracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.macrotracker.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button buttonSaveGoals;

  @NonNull
  public final TextInputEditText editTextCaloriesGoal;

  @NonNull
  public final TextInputEditText editTextCarbsGoal;

  @NonNull
  public final TextInputEditText editTextFatGoal;

  @NonNull
  public final TextInputEditText editTextProteinGoal;

  @NonNull
  public final ProgressBar progressBar;

  private FragmentSettingsBinding(@NonNull ScrollView rootView, @NonNull Button buttonSaveGoals,
      @NonNull TextInputEditText editTextCaloriesGoal, @NonNull TextInputEditText editTextCarbsGoal,
      @NonNull TextInputEditText editTextFatGoal, @NonNull TextInputEditText editTextProteinGoal,
      @NonNull ProgressBar progressBar) {
    this.rootView = rootView;
    this.buttonSaveGoals = buttonSaveGoals;
    this.editTextCaloriesGoal = editTextCaloriesGoal;
    this.editTextCarbsGoal = editTextCarbsGoal;
    this.editTextFatGoal = editTextFatGoal;
    this.editTextProteinGoal = editTextProteinGoal;
    this.progressBar = progressBar;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_save_goals;
      Button buttonSaveGoals = ViewBindings.findChildViewById(rootView, id);
      if (buttonSaveGoals == null) {
        break missingId;
      }

      id = R.id.edit_text_calories_goal;
      TextInputEditText editTextCaloriesGoal = ViewBindings.findChildViewById(rootView, id);
      if (editTextCaloriesGoal == null) {
        break missingId;
      }

      id = R.id.edit_text_carbs_goal;
      TextInputEditText editTextCarbsGoal = ViewBindings.findChildViewById(rootView, id);
      if (editTextCarbsGoal == null) {
        break missingId;
      }

      id = R.id.edit_text_fat_goal;
      TextInputEditText editTextFatGoal = ViewBindings.findChildViewById(rootView, id);
      if (editTextFatGoal == null) {
        break missingId;
      }

      id = R.id.edit_text_protein_goal;
      TextInputEditText editTextProteinGoal = ViewBindings.findChildViewById(rootView, id);
      if (editTextProteinGoal == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((ScrollView) rootView, buttonSaveGoals,
          editTextCaloriesGoal, editTextCarbsGoal, editTextFatGoal, editTextProteinGoal,
          progressBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
