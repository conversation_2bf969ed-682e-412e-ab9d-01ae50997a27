package com.macrotracker.app.ui.dashboard

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer
import com.macrotracker.app.MacroTrackerApplication
import com.macrotracker.app.R
import com.macrotracker.app.data.repository.MacroRepository
import com.macrotracker.app.databinding.FragmentDashboardBinding
import com.macrotracker.app.service.FoodAnalysisService
import com.macrotracker.app.ui.ViewModelFactory

class DashboardFragment : Fragment() {
    
    private var _binding: FragmentDashboardBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: DashboardViewModel by viewModels {
        val app = requireActivity().application as MacroTrackerApplication
        val repository = MacroRepository(
            app.database.macroEntryDao(),
            app.database.dailyGoalsDao()
        )
        val foodAnalysisService = FoodAnalysisService()
        ViewModelFactory(repository, foodAnalysisService)
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDashboardBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupObservers()
        setupUI()
    }
    
    private fun setupObservers() {
        viewModel.dailySummary.observe(viewLifecycleOwner, Observer { summary ->
            updateMacroDisplay(summary)
        })
        
        viewModel.dailyGoals.observe(viewLifecycleOwner, Observer { goals ->
            updateGoalsDisplay(goals)
        })
        
        viewModel.isLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        })
    }
    
    private fun setupUI() {
        // Setup any click listeners or initial UI state
        binding.refreshButton.setOnClickListener {
            viewModel.refreshData()
        }
    }
    
    private fun updateMacroDisplay(summary: com.macrotracker.app.data.entity.DailySummary) {
        // Update calories
        binding.caloriesProgress.progress = (summary.getCaloriesProgress(viewModel.dailyGoals.value?.caloriesGoal ?: 2000.0) * 100).toInt()
        binding.caloriesText.text = "${summary.totalCalories.toInt()} / ${viewModel.dailyGoals.value?.caloriesGoal?.toInt() ?: 2000}"
        
        // Update protein
        binding.proteinProgress.progress = (summary.getProteinProgress(viewModel.dailyGoals.value?.proteinGoal ?: 150.0) * 100).toInt()
        binding.proteinText.text = "${summary.totalProtein.toInt()}g / ${viewModel.dailyGoals.value?.proteinGoal?.toInt() ?: 150}g"
        
        // Update carbs
        binding.carbsProgress.progress = (summary.getCarbsProgress(viewModel.dailyGoals.value?.carbsGoal ?: 250.0) * 100).toInt()
        binding.carbsText.text = "${summary.totalCarbs.toInt()}g / ${viewModel.dailyGoals.value?.carbsGoal?.toInt() ?: 250}g"
        
        // Update fat
        binding.fatProgress.progress = (summary.getFatProgress(viewModel.dailyGoals.value?.fatGoal ?: 65.0) * 100).toInt()
        binding.fatText.text = "${summary.totalFat.toInt()}g / ${viewModel.dailyGoals.value?.fatGoal?.toInt() ?: 65}g"
    }
    
    private fun updateGoalsDisplay(goals: com.macrotracker.app.data.entity.DailyGoals?) {
        goals?.let {
            // Goals are used in updateMacroDisplay, so refresh the display
            viewModel.dailySummary.value?.let { summary ->
                updateMacroDisplay(summary)
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
