[{"merged": "com.macrotracker.app-mergeDebugResources-3:/layout/fragment_settings.xml", "source": "com.macrotracker.app-main-6:/layout/fragment_settings.xml"}, {"merged": "com.macrotracker.app-mergeDebugResources-3:/layout/fragment_history.xml", "source": "com.macrotracker.app-main-6:/layout/fragment_history.xml"}, {"merged": "com.macrotracker.app-mergeDebugResources-3:/layout/item_macro_entry.xml", "source": "com.macrotracker.app-main-6:/layout/item_macro_entry.xml"}, {"merged": "com.macrotracker.app-mergeDebugResources-3:/layout/activity_main.xml", "source": "com.macrotracker.app-main-6:/layout/activity_main.xml"}, {"merged": "com.macrotracker.app-mergeDebugResources-3:/layout/fragment_dashboard.xml", "source": "com.macrotracker.app-main-6:/layout/fragment_dashboard.xml"}, {"merged": "com.macrotracker.app-mergeDebugResources-3:/layout/fragment_food_entry.xml", "source": "com.macrotracker.app-main-6:/layout/fragment_food_entry.xml"}]