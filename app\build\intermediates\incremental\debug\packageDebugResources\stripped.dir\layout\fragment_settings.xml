<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/nav_settings"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/daily_goals"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Calories Goal -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="@string/calories_goal"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_calories_goal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                tools:text="2000" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Protein Goal -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="@string/protein_goal"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_protein_goal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                tools:text="150" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Carbs Goal -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="@string/carbs_goal"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_carbs_goal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                tools:text="250" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Fat Goal -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="@string/fat_goal"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_fat_goal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                tools:text="65" />

        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/button_save_goals"
            style="@style/MacroButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Save Goals" />

        <!-- App Info Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="About"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Macro Tracker v1.0\nPowered by Gemini AI"
            android:textSize="14sp"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

</ScrollView>
