{"logs": [{"outputFile": "com.macrotracker.app-mergeDebugResources-2:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c9b3062a3a9baebe8ed60ea9c7199b7\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,274,357,456,555,637,722,813,899,979,1058,1140,1213,1297,1372,1456,1537,1618,1685", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "269,352,451,550,632,717,808,894,974,1053,1135,1208,1292,1367,1451,1532,1613,1680,1798"}, "to": {"startLines": "49,50,51,52,53,58,59,179,180,182,183,187,189,190,191,192,194,195,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4553,4642,4725,4824,4923,5286,5371,16578,16664,16824,16903,17227,17376,17460,17535,17619,17801,17882,17949", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "4637,4720,4819,4918,5000,5366,5457,16659,16739,16898,16980,17295,17455,17530,17614,17695,17877,17944,18062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8794f68105cf4d13d6ceb8339e99e2bd\\transformed\\core-1.16.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3521,3623,3726,3831,3936,4035,4139,17700", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3618,3721,3826,3931,4030,4134,4248,17796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831512154db5267262fed228477d4e6b\\transformed\\material3-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,399,516,614,711,825,954,1074,1213,1297,1403,1494,1591,1705,1833,1944,2072,2198,2330,2503,2627,2744,2864,2985,3077,3172,3291,3412,3513,3616,3720,3851,3987,4094,4191,4267,4363,4461,4566,4652,4741,4835,4918,5001,5100,5200,5292,5393,5481,5592,5694,5806,5927,6009,6117", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "166,283,394,511,609,706,820,949,1069,1208,1292,1398,1489,1586,1700,1828,1939,2067,2193,2325,2498,2622,2739,2859,2980,3072,3167,3286,3407,3508,3611,3715,3846,3982,4089,4186,4262,4358,4456,4561,4647,4736,4830,4913,4996,5095,5195,5287,5388,5476,5587,5689,5801,5922,6004,6112,6211"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5522,5638,5755,5866,5983,6081,6178,6292,6421,6541,6680,6764,6870,6961,7058,7172,7300,7411,7539,7665,7797,7970,8094,8211,8331,8452,8544,8639,8758,8879,8980,9083,9187,9318,9454,9561,9658,9734,9830,9928,10033,10119,10208,10302,10385,10468,10567,10667,10759,10860,10948,11059,11161,11273,11394,11476,11584", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "5633,5750,5861,5978,6076,6173,6287,6416,6536,6675,6759,6865,6956,7053,7167,7295,7406,7534,7660,7792,7965,8089,8206,8326,8447,8539,8634,8753,8874,8975,9078,9182,9313,9449,9556,9653,9729,9825,9923,10028,10114,10203,10297,10380,10463,10562,10662,10754,10855,10943,11054,11156,11268,11389,11471,11579,11678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5378ea3a25a7f71addffda9b7a578b53\\transformed\\navigation-ui-2.9.3\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16350,16461", "endColumns": "110,116", "endOffsets": "16456,16573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00d4f69db0bff584c09211a0d3ca791f\\transformed\\material-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12641", "endColumns": "87", "endOffsets": "12724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6511df1e446016865fae0e249649412\\transformed\\material-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1037,1101,1190,1257,1317,1411,1475,1538,1594,1664,1731,1786,1905,1962,2026,2080,2153,2275,2358,2441,2534,2620,2705,2837,2915,2995,3117,3203,3287,3347,3399,3465,3535,3608,3679,3756,3828,3905,3977,4047,4160,4253,4326,4416,4509,4583,4655,4746,4800,4880,4946,5030,5115,5177,5241,5304,5370,5475,5580,5675,5776,5840,5896,5976,6061,6136", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "264,340,417,495,586,671,773,888,971,1032,1096,1185,1252,1312,1406,1470,1533,1589,1659,1726,1781,1900,1957,2021,2075,2148,2270,2353,2436,2529,2615,2700,2832,2910,2990,3112,3198,3282,3342,3394,3460,3530,3603,3674,3751,3823,3900,3972,4042,4155,4248,4321,4411,4504,4578,4650,4741,4795,4875,4941,5025,5110,5172,5236,5299,5365,5470,5575,5670,5771,5835,5891,5971,6056,6131,6207"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3114,3190,3267,3345,3436,4253,4355,4470,5005,5066,5130,5219,5462,11683,11777,11841,11904,11960,12030,12097,12152,12271,12328,12392,12446,12519,12729,12812,12895,12988,13074,13159,13291,13369,13449,13571,13657,13741,13801,13853,13919,13989,14062,14133,14210,14282,14359,14431,14501,14614,14707,14780,14870,14963,15037,15109,15200,15254,15334,15400,15484,15569,15631,15695,15758,15824,15929,16034,16129,16230,16294,16744,17067,17152,17300", "endLines": "5,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "314,3185,3262,3340,3431,3516,4350,4465,4548,5061,5125,5214,5281,5517,11772,11836,11899,11955,12025,12092,12147,12266,12323,12387,12441,12514,12636,12807,12890,12983,13069,13154,13286,13364,13444,13566,13652,13736,13796,13848,13914,13984,14057,14128,14205,14277,14354,14426,14496,14609,14702,14775,14865,14958,15032,15104,15195,15249,15329,15395,15479,15564,15626,15690,15753,15819,15924,16029,16124,16225,16289,16345,16819,17147,17222,17371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e2ee5ef775fc5fd4089dfad5d6aab731\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,16985", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,17062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f09420083b708841bdec860b0e3e3c1b\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,224", "endColumns": "78,89,92", "endOffsets": "129,219,312"}, "to": {"startLines": "33,197,198", "startColumns": "4,4,4", "startOffsets": "3035,18067,18157", "endColumns": "78,89,92", "endOffsets": "3109,18152,18245"}}]}]}