package com.macrotracker.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "daily_goals")
data class DailyGoals(
    @PrimaryKey
    val date: String, // Format: yyyy-MM-dd
    val caloriesGoal: Double = 2000.0,
    val proteinGoal: Double = 150.0, // in grams
    val carbsGoal: Double = 250.0, // in grams
    val fatGoal: Double = 65.0, // in grams
    val fiberGoal: Double = 25.0, // in grams
    val sugarGoal: Double = 50.0, // in grams
    val sodiumGoal: Double = 2300.0, // in milligrams
    val updatedAt: Date = Date()
)
