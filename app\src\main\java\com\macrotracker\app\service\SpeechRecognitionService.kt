package com.macrotracker.app.service

import android.content.Context
import android.content.Intent
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.speech.RecognitionListener
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import java.util.*

class SpeechRecognitionService(private val context: Context) {
    
    private var speechRecognizer: SpeechRecognizer? = null
    private var isListening = false
    
    val isListeningLiveData = MutableLiveData<Boolean>()
    val speechResultLiveData = MutableLiveData<String>()
    val errorLiveData = MutableLiveData<String>()
    
    fun startListening() {
        if (isListening) return
        
        if (!SpeechRecognizer.isRecognitionAvailable(context)) {
            errorLiveData.value = "Speech recognition not available"
            return
        }
        
        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
        speechRecognizer?.setRecognitionListener(recognitionListener)
        
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PROMPT, "Describe the food you ate...")
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
        }
        
        speechRecognizer?.startListening(intent)
        isListening = true
        isListeningLiveData.value = true
    }
    
    fun stopListening() {
        speechRecognizer?.stopListening()
        isListening = false
        isListeningLiveData.value = false
    }
    
    fun destroy() {
        speechRecognizer?.destroy()
        speechRecognizer = null
        isListening = false
        isListeningLiveData.value = false
    }
    
    private val recognitionListener = object : RecognitionListener {
        override fun onReadyForSpeech(params: Bundle?) {
            // Speech recognition is ready
        }
        
        override fun onBeginningOfSpeech() {
            // User started speaking
        }
        
        override fun onRmsChanged(rmsdB: Float) {
            // Volume level changed
        }
        
        override fun onBufferReceived(buffer: ByteArray?) {
            // Audio buffer received
        }
        
        override fun onEndOfSpeech() {
            // User stopped speaking
            isListening = false
            isListeningLiveData.value = false
        }
        
        override fun onError(error: Int) {
            isListening = false
            isListeningLiveData.value = false
            
            val errorMessage = when (error) {
                SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                SpeechRecognizer.ERROR_NETWORK -> "Network error"
                SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                SpeechRecognizer.ERROR_NO_MATCH -> "No speech input matched"
                SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                SpeechRecognizer.ERROR_SERVER -> "Server error"
                SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                else -> "Unknown error occurred"
            }
            
            errorLiveData.value = errorMessage
        }
        
        override fun onResults(results: Bundle?) {
            isListening = false
            isListeningLiveData.value = false
            
            val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
            if (!matches.isNullOrEmpty()) {
                speechResultLiveData.value = matches[0]
            } else {
                errorLiveData.value = "No speech recognized"
            }
        }
        
        override fun onPartialResults(partialResults: Bundle?) {
            // Partial results received (optional to handle)
        }
        
        override fun onEvent(eventType: Int, params: Bundle?) {
            // Reserved for future use
        }
    }
}
