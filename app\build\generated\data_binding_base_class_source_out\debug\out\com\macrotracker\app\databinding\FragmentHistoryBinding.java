// Generated by view binder compiler. Do not edit!
package com.macrotracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.macrotracker.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHistoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewHistory;

  @NonNull
  public final TextView textViewEmpty;

  private FragmentHistoryBinding(@NonNull LinearLayout rootView, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewHistory, @NonNull TextView textViewEmpty) {
    this.rootView = rootView;
    this.progressBar = progressBar;
    this.recyclerViewHistory = recyclerViewHistory;
    this.textViewEmpty = textViewEmpty;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_history;
      RecyclerView recyclerViewHistory = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewHistory == null) {
        break missingId;
      }

      id = R.id.text_view_empty;
      TextView textViewEmpty = ViewBindings.findChildViewById(rootView, id);
      if (textViewEmpty == null) {
        break missingId;
      }

      return new FragmentHistoryBinding((LinearLayout) rootView, progressBar, recyclerViewHistory,
          textViewEmpty);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
