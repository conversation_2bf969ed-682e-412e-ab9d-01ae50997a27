package com.macrotracker.app.ui.history

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.macrotracker.app.MacroTrackerApplication
import com.macrotracker.app.data.repository.MacroRepository
import com.macrotracker.app.databinding.FragmentHistoryBinding
import com.macrotracker.app.service.FoodAnalysisService
import com.macrotracker.app.ui.ViewModelFactory

class HistoryFragment : Fragment() {
    
    private var _binding: FragmentHistoryBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: HistoryViewModel by viewModels {
        val app = requireActivity().application as MacroTrackerApplication
        val repository = MacroRepository(
            app.database.macroEntryDao(),
            app.database.dailyGoalsDao()
        )
        val foodAnalysisService = FoodAnalysisService()
        ViewModelFactory(repository, foodAnalysisService)
    }
    private lateinit var historyAdapter: HistoryAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHistoryBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()
        setupObservers()
    }
    
    private fun setupRecyclerView() {
        historyAdapter = HistoryAdapter { entry ->
            // Handle item click - could show details or allow editing
            viewModel.selectEntry(entry)
        }
        
        binding.recyclerViewHistory.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = historyAdapter
        }
    }
    
    private fun setupObservers() {
        viewModel.entries.observe(viewLifecycleOwner) { entries ->
            historyAdapter.submitList(entries)
            binding.textViewEmpty.visibility = if (entries.isEmpty()) View.VISIBLE else View.GONE
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
