-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml:48:9-56:20
	android:grantUriPermissions
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:52:13-47
	android:authorities
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:50:13-64
	android:exported
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:51:13-37
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:49:13-62
manifest
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml:2:1-60:12
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml:2:1-60:12
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee015cc4052d32b03ecaa3ede2427982\transformed\viewbinding-8.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.37.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e45a07d8520b00f03c28a28e0254c89d\transformed\accompanist-permissions-0.37.3\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8b09414fb4a0e50bfea76091af6db7f\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e4a5a18da7040fc1e7ee7111f423a24\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93be5b93d04910f7381fbce91c9261ac\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5378ea3a25a7f71addffda9b7a578b53\transformed\navigation-ui-2.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1bc9702d3ad2644836c4bdfa288bb8e\transformed\navigation-ui-ktx-2.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91d254e1a80de84c8849161adc5950d6\transformed\navigation-fragment-ktx-2.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25cb0457e7d21a8756a418cbc7d77198\transformed\navigation-fragment-2.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6511df1e446016865fae0e249649412\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-video:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a065efb8bcbaa8c98f4e2fea2115465e\transformed\camera-video-1.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03e9e6be53b1c72215fba375f566f6a1\transformed\camera-lifecycle-1.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac57036963116f6f08d183adfd808c12\transformed\camera-view-1.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d0e8f7e5488366408acf51a2264aa5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e47edf71460398665ce2a8b0ec2fc74b\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c73ae70cd6221832b2144606f781e3a\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c609276e7e7362f1171afe1ac9613a\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\325606018359731246687000c5e49a99\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d59155a31cfc64ef596322260a22c801\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2ee5ef775fc5fd4089dfad5d6aab731\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0da1700ab9e8b70f41385112e7b5ea86\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.8.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1e3d56a172259be30253cdd810f9441\transformed\fragment-1.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcffe54f3f5786e1b24e46b13852a8a4\transformed\fragment-ktx-1.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831512154db5267262fed228477d4e6b\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00d4f69db0bff584c09211a0d3ca791f\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60823f17511b0184ecceb630450655f6\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcabe3516f211be4993e101db28031c5\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204c504bf7600e6df0774c27ea283bce\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47795adc052a04f4ec0de3a74a90b70b\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09420083b708841bdec860b0e3e3c1b\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b7fd3c7cf8d3bd72e7f4940f5f4135\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d33bdf883bcf664cb2629ab576ff120f\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c08a9b417ac34aa341befb5f414e7815\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27f5b7f0b76fef2bbade98ae34d209f1\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e7f86cc152da248d5e0821adae7acd5\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a92c72a7275db0cae5c4bbecab790da\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1d26f44671e6bcc4a512bb54f5fd7f6\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe9044471411f75bc61436f4e43bb20f\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e745a9f1052c51538e2768f6ce7c446\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c72b7215aa81f26a8abbe6a26155d13\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eef1407fdb5c731837d4573875d5aa91\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7d530257d63350f876ccc4739637b45\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2131d0e11c55b4d2b2c8d732fb86ec7d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58c705e6d62bae9760e20299aae3d09\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17b7ea4f559f1b2710ba25ccb0a6f2a2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53bb23f8888906d327f7a61f2a262068\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20c31af3c3027334491c5fa5a4f96306\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\241d62afd94abbed09565b196085a0dc\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfec3f7c081a5848005992ffadd69c80\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b16fa18b47d9837b94a80cc1f8ca8979\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58f9f34b1717f9e762bc41d95fcd4525\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfc38458ae435a62e2d503e037a407de\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\485c62d4ffb9bffa4ae3da4d7c45b4d9\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeb36c691539b697ac14b307057d9af2\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19bcb5a34b3139e7411ba21f2af03ba5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c76ea2d4473bb0e7622c558bd8e4862\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a36ef7c08636194c120b065178ea9\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0db6520ef7ac3f6b0c9b16ea571dbe9\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01463aaafb40561ec933a09109d69835\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\651b1c8c5259a72b3d3131d668f20f29\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cb6ba46c15c1d9b85de443fcb0822ef\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b81931ecbb1d1ca69ed595e883718bf\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab74137bdc0dea50f8af591c287aa769\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\129b5dbe4879e505e1d7bc2ab5fb9834\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7708bc1faf16d357dac25151cf801b89\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a8055dfa8b8056d8763e7248e0727b\transformed\lifecycle-livedata-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\801575965c35ec334a5b411677fce375\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba55fb9d1b4ba5ee5c5edd3a4c9778e9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b09a3457e8d98f92a5c98fe2730a6b\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fec246864b9a097a839ceaa91119da24\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c9b3062a3a9baebe8ed60ea9c7199b7\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24dbea1b5015b06ee47501c72d867b32\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26c038e092c24c0c4d43ab660d2fc167\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60176b414c838c15f3e6c0d7591b8b0c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\287e904fdfaad8146be99c423b40f7ba\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ec0a8d7203897fdc53866f7697fa513\transformed\room-ktx-2.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6aea3e6112b09a728aaafe40a7c815b\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b8e1fa2a1e58301e86b4f566a9ab819\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c8dcddb139c9ac526002aa10d25fc24\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\345073aea2950015b2f60cd54b2cec3e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e29980bbdb2348faad73b73e38e05ee\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d0c35c9f47cbf82214f5e6f79e7283f\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3219973e93039e075d4f0194cd67faf8\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b508d0f167086047bf9de2270c707d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b39b9f88399d5bb2f9cce4ea4cd47e19\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7559f8debb25886fe13faeae5bdaee08\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bc593a46f6a8827e3e9a06ee7e17c3d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f0110faf6fe41844774b2a35c5c247a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e6c4886b293a1cd1944efaa1b2f968\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9afc39b8d4bf07e6e0fc5e1084fca01\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\798e72ddecb21b4c980bc2a12b3f0011\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30bb8a01d6996761accd522f2288c0f1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bff7d1f9a020fd46bf591bdf8a00772a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ce783048cfb9f5426166a5d4ab67c00\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:8:5-67
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:8:5-67
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.CAMERA
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:8:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:11:22-77
uses-feature#android.hardware.camera
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:17:9-33
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:16:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:18:5-20:36
	android:required
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:20:9-33
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:19:9-57
uses-feature#android.hardware.microphone
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:21:5-23:36
	android:required
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:23:9-33
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:22:9-51
application
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:25:5-58:19
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml:25:5-58:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6511df1e446016865fae0e249649412\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6511df1e446016865fae0e249649412\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d0e8f7e5488366408acf51a2264aa5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d0e8f7e5488366408acf51a2264aa5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:10:5-20:19
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:10:5-20:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7559f8debb25886fe13faeae5bdaee08\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7559f8debb25886fe13faeae5bdaee08\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:33:9-35
	android:label
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:31:9-41
	android:fullBackupContent
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:29:9-54
	android:roundIcon
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:32:9-54
	tools:targetApi
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:35:9-29
	android:icon
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:30:9-43
	android:allowBackup
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:27:9-35
	android:theme
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:34:9-50
	android:dataExtractionRules
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:28:9-65
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:26:9-48
activity#com.macrotracker.app.MainActivity
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:37:9-45:20
	android:exported
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:39:13-36
	android:theme
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:40:13-54
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:38:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:41:13-44:29
action#android.intent.action.MAIN
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:43:17-77
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:43:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:53:13-55:54
	android:resource
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:55:17-51
	android:name
		ADDED from C:\MyCal\app\src\main\AndroidManifest.xml:54:17-67
uses-sdk
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee015cc4052d32b03ecaa3ede2427982\transformed\viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee015cc4052d32b03ecaa3ede2427982\transformed\viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.37.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e45a07d8520b00f03c28a28e0254c89d\transformed\accompanist-permissions-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.37.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e45a07d8520b00f03c28a28e0254c89d\transformed\accompanist-permissions-0.37.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8b09414fb4a0e50bfea76091af6db7f\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8b09414fb4a0e50bfea76091af6db7f\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e4a5a18da7040fc1e7ee7111f423a24\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e4a5a18da7040fc1e7ee7111f423a24\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93be5b93d04910f7381fbce91c9261ac\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93be5b93d04910f7381fbce91c9261ac\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5378ea3a25a7f71addffda9b7a578b53\transformed\navigation-ui-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5378ea3a25a7f71addffda9b7a578b53\transformed\navigation-ui-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1bc9702d3ad2644836c4bdfa288bb8e\transformed\navigation-ui-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1bc9702d3ad2644836c4bdfa288bb8e\transformed\navigation-ui-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91d254e1a80de84c8849161adc5950d6\transformed\navigation-fragment-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91d254e1a80de84c8849161adc5950d6\transformed\navigation-fragment-ktx-2.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25cb0457e7d21a8756a418cbc7d77198\transformed\navigation-fragment-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25cb0457e7d21a8756a418cbc7d77198\transformed\navigation-fragment-2.9.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6511df1e446016865fae0e249649412\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6511df1e446016865fae0e249649412\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a065efb8bcbaa8c98f4e2fea2115465e\transformed\camera-video-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a065efb8bcbaa8c98f4e2fea2115465e\transformed\camera-video-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03e9e6be53b1c72215fba375f566f6a1\transformed\camera-lifecycle-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03e9e6be53b1c72215fba375f566f6a1\transformed\camera-lifecycle-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac57036963116f6f08d183adfd808c12\transformed\camera-view-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac57036963116f6f08d183adfd808c12\transformed\camera-view-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d0e8f7e5488366408acf51a2264aa5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d0e8f7e5488366408acf51a2264aa5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e47edf71460398665ce2a8b0ec2fc74b\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e47edf71460398665ce2a8b0ec2fc74b\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c73ae70cd6221832b2144606f781e3a\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c73ae70cd6221832b2144606f781e3a\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c609276e7e7362f1171afe1ac9613a\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c609276e7e7362f1171afe1ac9613a\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\325606018359731246687000c5e49a99\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\325606018359731246687000c5e49a99\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d59155a31cfc64ef596322260a22c801\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d59155a31cfc64ef596322260a22c801\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2ee5ef775fc5fd4089dfad5d6aab731\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2ee5ef775fc5fd4089dfad5d6aab731\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0da1700ab9e8b70f41385112e7b5ea86\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0da1700ab9e8b70f41385112e7b5ea86\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.8.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1e3d56a172259be30253cdd810f9441\transformed\fragment-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1e3d56a172259be30253cdd810f9441\transformed\fragment-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcffe54f3f5786e1b24e46b13852a8a4\transformed\fragment-ktx-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcffe54f3f5786e1b24e46b13852a8a4\transformed\fragment-ktx-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831512154db5267262fed228477d4e6b\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831512154db5267262fed228477d4e6b\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00d4f69db0bff584c09211a0d3ca791f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00d4f69db0bff584c09211a0d3ca791f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60823f17511b0184ecceb630450655f6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60823f17511b0184ecceb630450655f6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcabe3516f211be4993e101db28031c5\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcabe3516f211be4993e101db28031c5\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204c504bf7600e6df0774c27ea283bce\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204c504bf7600e6df0774c27ea283bce\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47795adc052a04f4ec0de3a74a90b70b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47795adc052a04f4ec0de3a74a90b70b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09420083b708841bdec860b0e3e3c1b\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09420083b708841bdec860b0e3e3c1b\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b7fd3c7cf8d3bd72e7f4940f5f4135\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b7fd3c7cf8d3bd72e7f4940f5f4135\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d33bdf883bcf664cb2629ab576ff120f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d33bdf883bcf664cb2629ab576ff120f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c08a9b417ac34aa341befb5f414e7815\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c08a9b417ac34aa341befb5f414e7815\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27f5b7f0b76fef2bbade98ae34d209f1\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27f5b7f0b76fef2bbade98ae34d209f1\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e7f86cc152da248d5e0821adae7acd5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e7f86cc152da248d5e0821adae7acd5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a92c72a7275db0cae5c4bbecab790da\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a92c72a7275db0cae5c4bbecab790da\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1d26f44671e6bcc4a512bb54f5fd7f6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1d26f44671e6bcc4a512bb54f5fd7f6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe9044471411f75bc61436f4e43bb20f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe9044471411f75bc61436f4e43bb20f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e745a9f1052c51538e2768f6ce7c446\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e745a9f1052c51538e2768f6ce7c446\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c72b7215aa81f26a8abbe6a26155d13\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c72b7215aa81f26a8abbe6a26155d13\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eef1407fdb5c731837d4573875d5aa91\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eef1407fdb5c731837d4573875d5aa91\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7d530257d63350f876ccc4739637b45\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7d530257d63350f876ccc4739637b45\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2131d0e11c55b4d2b2c8d732fb86ec7d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2131d0e11c55b4d2b2c8d732fb86ec7d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58c705e6d62bae9760e20299aae3d09\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58c705e6d62bae9760e20299aae3d09\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17b7ea4f559f1b2710ba25ccb0a6f2a2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17b7ea4f559f1b2710ba25ccb0a6f2a2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53bb23f8888906d327f7a61f2a262068\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53bb23f8888906d327f7a61f2a262068\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20c31af3c3027334491c5fa5a4f96306\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20c31af3c3027334491c5fa5a4f96306\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\241d62afd94abbed09565b196085a0dc\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\241d62afd94abbed09565b196085a0dc\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfec3f7c081a5848005992ffadd69c80\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfec3f7c081a5848005992ffadd69c80\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b16fa18b47d9837b94a80cc1f8ca8979\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b16fa18b47d9837b94a80cc1f8ca8979\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58f9f34b1717f9e762bc41d95fcd4525\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58f9f34b1717f9e762bc41d95fcd4525\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfc38458ae435a62e2d503e037a407de\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfc38458ae435a62e2d503e037a407de\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\485c62d4ffb9bffa4ae3da4d7c45b4d9\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\485c62d4ffb9bffa4ae3da4d7c45b4d9\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeb36c691539b697ac14b307057d9af2\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeb36c691539b697ac14b307057d9af2\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19bcb5a34b3139e7411ba21f2af03ba5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19bcb5a34b3139e7411ba21f2af03ba5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c76ea2d4473bb0e7622c558bd8e4862\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c76ea2d4473bb0e7622c558bd8e4862\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a36ef7c08636194c120b065178ea9\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a36ef7c08636194c120b065178ea9\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0db6520ef7ac3f6b0c9b16ea571dbe9\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0db6520ef7ac3f6b0c9b16ea571dbe9\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01463aaafb40561ec933a09109d69835\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01463aaafb40561ec933a09109d69835\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\651b1c8c5259a72b3d3131d668f20f29\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\651b1c8c5259a72b3d3131d668f20f29\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cb6ba46c15c1d9b85de443fcb0822ef\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cb6ba46c15c1d9b85de443fcb0822ef\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b81931ecbb1d1ca69ed595e883718bf\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b81931ecbb1d1ca69ed595e883718bf\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab74137bdc0dea50f8af591c287aa769\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab74137bdc0dea50f8af591c287aa769\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\129b5dbe4879e505e1d7bc2ab5fb9834\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\129b5dbe4879e505e1d7bc2ab5fb9834\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7708bc1faf16d357dac25151cf801b89\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7708bc1faf16d357dac25151cf801b89\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a8055dfa8b8056d8763e7248e0727b\transformed\lifecycle-livedata-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a8055dfa8b8056d8763e7248e0727b\transformed\lifecycle-livedata-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\801575965c35ec334a5b411677fce375\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\801575965c35ec334a5b411677fce375\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba55fb9d1b4ba5ee5c5edd3a4c9778e9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba55fb9d1b4ba5ee5c5edd3a4c9778e9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b09a3457e8d98f92a5c98fe2730a6b\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b09a3457e8d98f92a5c98fe2730a6b\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fec246864b9a097a839ceaa91119da24\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fec246864b9a097a839ceaa91119da24\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c9b3062a3a9baebe8ed60ea9c7199b7\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c9b3062a3a9baebe8ed60ea9c7199b7\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24dbea1b5015b06ee47501c72d867b32\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24dbea1b5015b06ee47501c72d867b32\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26c038e092c24c0c4d43ab660d2fc167\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26c038e092c24c0c4d43ab660d2fc167\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60176b414c838c15f3e6c0d7591b8b0c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60176b414c838c15f3e6c0d7591b8b0c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\287e904fdfaad8146be99c423b40f7ba\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\287e904fdfaad8146be99c423b40f7ba\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ec0a8d7203897fdc53866f7697fa513\transformed\room-ktx-2.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ec0a8d7203897fdc53866f7697fa513\transformed\room-ktx-2.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6aea3e6112b09a728aaafe40a7c815b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6aea3e6112b09a728aaafe40a7c815b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b8e1fa2a1e58301e86b4f566a9ab819\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b8e1fa2a1e58301e86b4f566a9ab819\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:6:5-44
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:6:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c8dcddb139c9ac526002aa10d25fc24\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c8dcddb139c9ac526002aa10d25fc24\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\345073aea2950015b2f60cd54b2cec3e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\345073aea2950015b2f60cd54b2cec3e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e29980bbdb2348faad73b73e38e05ee\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e29980bbdb2348faad73b73e38e05ee\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d0c35c9f47cbf82214f5e6f79e7283f\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d0c35c9f47cbf82214f5e6f79e7283f\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3219973e93039e075d4f0194cd67faf8\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3219973e93039e075d4f0194cd67faf8\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b508d0f167086047bf9de2270c707d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78b508d0f167086047bf9de2270c707d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b39b9f88399d5bb2f9cce4ea4cd47e19\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b39b9f88399d5bb2f9cce4ea4cd47e19\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7559f8debb25886fe13faeae5bdaee08\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7559f8debb25886fe13faeae5bdaee08\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bc593a46f6a8827e3e9a06ee7e17c3d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bc593a46f6a8827e3e9a06ee7e17c3d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f0110faf6fe41844774b2a35c5c247a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f0110faf6fe41844774b2a35c5c247a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e6c4886b293a1cd1944efaa1b2f968\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e6c4886b293a1cd1944efaa1b2f968\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9afc39b8d4bf07e6e0fc5e1084fca01\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9afc39b8d4bf07e6e0fc5e1084fca01\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\798e72ddecb21b4c980bc2a12b3f0011\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\798e72ddecb21b4c980bc2a12b3f0011\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30bb8a01d6996761accd522f2288c0f1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30bb8a01d6996761accd522f2288c0f1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bff7d1f9a020fd46bf591bdf8a00772a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bff7d1f9a020fd46bf591bdf8a00772a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ce783048cfb9f5426166a5d4ab67c00\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ce783048cfb9f5426166a5d4ab67c00\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\MyCal\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:24:9-33:19
	android:enabled
		ADDED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:31:13-36
	android:exported
		ADDED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:32:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:33:13-75
	android:name
		ADDED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:30:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:11:9-19:20
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:11:9-19:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\454c34b1b8b158f56142095553c39734\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.macrotracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.macrotracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#okhttp3.internal.platform.PlatformInitializer
ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:16:13-18:52
	android:value
		ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:18:17-49
	android:name
		ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:17:17-77
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
