package com.macrotracker.app.data.repository

import androidx.lifecycle.LiveData
import com.macrotracker.app.data.dao.DailyGoalsDao
import com.macrotracker.app.data.dao.MacroEntryDao
import com.macrotracker.app.data.entity.DailyGoals
import com.macrotracker.app.data.entity.DailySummary
import com.macrotracker.app.data.entity.MacroEntry
import java.text.SimpleDateFormat
import java.util.*
class MacroRepository(
    private val macroEntryDao: MacroEntryDao,
    private val dailyGoalsDao: DailyGoalsDao
) {
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    // Macro Entries
    fun getAllEntries(): LiveData<List<MacroEntry>> = macroEntryDao.getAllEntries()
    
    fun getEntriesForDate(date: Date): LiveData<List<MacroEntry>> = 
        macroEntryDao.getEntriesForDate(date)
    
    suspend fun getEntriesForDateSync(date: Date): List<MacroEntry> = 
        macroEntryDao.getEntriesForDateSync(date)
    
    suspend fun insertEntry(entry: MacroEntry): Long = macroEntryDao.insertEntry(entry)
    
    suspend fun updateEntry(entry: MacroEntry) = macroEntryDao.updateEntry(entry)
    
    suspend fun deleteEntry(entry: MacroEntry) = macroEntryDao.deleteEntry(entry)
    
    suspend fun deleteEntryById(id: Long) = macroEntryDao.deleteEntryById(id)
    
    suspend fun getEntryById(id: Long): MacroEntry? = macroEntryDao.getEntryById(id)
    
    // Daily Summary
    suspend fun getDailySummary(date: Date): DailySummary {
        val result = macroEntryDao.getDailySummary(date)
        return DailySummary(
            date = dateFormat.format(date),
            totalCalories = result.totalCalories,
            totalProtein = result.totalProtein,
            totalCarbs = result.totalCarbs,
            totalFat = result.totalFat,
            totalFiber = result.totalFiber,
            totalSugar = result.totalSugar,
            totalSodium = result.totalSodium,
            entryCount = result.entryCount
        )
    }
    
    // Daily Goals
    suspend fun getGoalsForDate(date: Date): DailyGoals {
        val dateString = dateFormat.format(date)
        return dailyGoalsDao.getGoalsForDate(dateString) 
            ?: getLatestGoals()?.copy(date = dateString) 
            ?: DailyGoals(date = dateString)
    }
    
    fun getGoalsForDateLive(date: Date): LiveData<DailyGoals?> {
        val dateString = dateFormat.format(date)
        return dailyGoalsDao.getGoalsForDateLive(dateString)
    }
    
    suspend fun saveGoals(goals: DailyGoals) = dailyGoalsDao.insertOrUpdateGoals(goals)
    
    private suspend fun getLatestGoals(): DailyGoals? = dailyGoalsDao.getLatestGoals()
    
    // Utility functions
    fun getTodayDate(): Date = Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }.time
    
    fun formatDate(date: Date): String = dateFormat.format(date)
}
