package com.macrotracker.app.ui.settings

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.macrotracker.app.data.entity.DailyGoals
import com.macrotracker.app.data.repository.MacroRepository
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
class SettingsViewModel(
    private val repository: MacroRepository
) : ViewModel() {
    
    private val _currentGoals = MutableLiveData<DailyGoals?>()
    val currentGoals: LiveData<DailyGoals?> = _currentGoals
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    init {
        loadCurrentGoals()
    }
    
    private fun loadCurrentGoals() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val today = repository.getTodayDate()
                val goals = repository.getGoalsForDate(today)
                _currentGoals.value = goals
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load goals: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun saveGoals(calories: Double, protein: Double, carbs: Double, fat: Double) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                _successMessage.value = null
                
                val today = repository.getTodayDate()
                val todayString = dateFormat.format(today)
                
                val goals = DailyGoals(
                    date = todayString,
                    caloriesGoal = calories,
                    proteinGoal = protein,
                    carbsGoal = carbs,
                    fatGoal = fat,
                    updatedAt = Date()
                )
                
                repository.saveGoals(goals)
                _currentGoals.value = goals
                _successMessage.value = "Goals saved successfully!"
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to save goals: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun clearMessages() {
        _errorMessage.value = null
        _successMessage.value = null
    }
}
