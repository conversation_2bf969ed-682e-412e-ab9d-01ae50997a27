<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.MacroTracker" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorOnError">@color/on_error</item>
    </style>

    <style name="Theme.MacroTracker" parent="Base.Theme.MacroTracker" />
    
    <!-- Custom button styles -->
    <style name="MacroButton" parent="Widget.Material3.Button">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="MacroButtonOutlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <!-- Card styles -->
    <style name="MacroCard" parent="Widget.Material3.CardView.Elevated">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
    </style>
</resources>
