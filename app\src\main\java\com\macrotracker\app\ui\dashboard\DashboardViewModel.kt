package com.macrotracker.app.ui.dashboard

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.macrotracker.app.data.entity.DailyGoals
import com.macrotracker.app.data.entity.DailySummary
import com.macrotracker.app.data.repository.MacroRepository
import kotlinx.coroutines.launch
class DashboardViewModel(
    private val repository: MacroRepository
) : ViewModel() {
    
    private val _dailySummary = MutableLiveData<DailySummary>()
    val dailySummary: LiveData<DailySummary> = _dailySummary
    
    private val _dailyGoals = MutableLiveData<DailyGoals>()
    val dailyGoals: LiveData<DailyGoals> = _dailyGoals
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    init {
        loadDashboardData()
    }
    
    fun refreshData() {
        loadDashboardData()
    }
    
    private fun loadDashboardData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val today = repository.getTodayDate()
                
                // Load daily summary
                val summary = repository.getDailySummary(today)
                _dailySummary.value = summary
                
                // Load daily goals
                val goals = repository.getGoalsForDate(today)
                _dailyGoals.value = goals
                
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load dashboard data: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
}
