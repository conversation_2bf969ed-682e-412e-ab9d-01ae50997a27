package com.macrotracker.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.macrotracker.app.data.converter.DateConverter
import com.macrotracker.app.data.dao.DailyGoalsDao
import com.macrotracker.app.data.dao.MacroEntryDao
import com.macrotracker.app.data.entity.DailyGoals
import com.macrotracker.app.data.entity.MacroEntry

@Database(
    entities = [MacroEntry::class, DailyGoals::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(DateConverter::class)
abstract class MacroDatabase : RoomDatabase() {
    
    abstract fun macroEntryDao(): MacroEntryDao
    abstract fun dailyGoalsDao(): DailyGoalsDao
    
    companion object {
        @Volatile
        private var INSTANCE: MacroDatabase? = null
        
        fun getDatabase(context: Context): MacroDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    MacroDatabase::class.java,
                    "macro_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
