<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background">#FFFFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="calories_color">#FF9C27B0</color>
    <color name="carbs_color">#FF4CAF50</color>
    <color name="error">#FFCF6679</color>
    <color name="fat_color">#FFFF9800</color>
    <color name="fiber_color">#FF795548</color>
    <color name="on_background">#FF000000</color>
    <color name="on_error">#FF000000</color>
    <color name="on_primary">#FFFFFFFF</color>
    <color name="on_secondary">#FF000000</color>
    <color name="on_surface">#FF000000</color>
    <color name="primary">#FF6200EE</color>
    <color name="primary_variant">#FF3700B3</color>
    <color name="progress_background">#FFE0E0E0</color>
    <color name="progress_high">#FFF44336</color>
    <color name="progress_low">#FF4CAF50</color>
    <color name="progress_medium">#FFFF9800</color>
    <color name="protein_color">#FF2196F3</color>
    <color name="secondary">#FF03DAC5</color>
    <color name="secondary_variant">#FF018786</color>
    <color name="sodium_color">#FF607D8B</color>
    <color name="sugar_color">#FFE91E63</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <string name="add_food">Add Food</string>
    <string name="analyzing_food">Analyzing food...</string>
    <string name="app_name">Macro Tracker</string>
    <string name="calories">Calories</string>
    <string name="calories_goal">Calories Goal</string>
    <string name="camera_permission_required">Camera permission is required to take photos</string>
    <string name="carbs">Carbs</string>
    <string name="carbs_goal">Carbs Goal</string>
    <string name="daily_goals">Daily Goals</string>
    <string name="daily_macros">Daily Macros</string>
    <string name="enter_food_manually">Enter food manually</string>
    <string name="error_api">Error analyzing food. Please try again.</string>
    <string name="error_camera">Camera error. Please try again.</string>
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_speech_recognition">Speech recognition failed. Please try again.</string>
    <string name="fat">Fat</string>
    <string name="fat_goal">Fat Goal</string>
    <string name="fiber">Fiber</string>
    <string name="food_name_hint">Enter food name (e.g., "1 cup rice")</string>
    <string name="grams">g</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="kcal">kcal</string>
    <string name="listening">Listening...</string>
    <string name="microphone_permission_required">Microphone permission is required for speech recognition</string>
    <string name="milligrams">mg</string>
    <string name="nav_add_food">Add Food</string>
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_history">History</string>
    <string name="nav_settings">Settings</string>
    <string name="protein">Protein</string>
    <string name="protein_goal">Protein Goal</string>
    <string name="set_goals">Set Goals</string>
    <string name="sodium">Sodium</string>
    <string name="speak_food">Speak food</string>
    <string name="sugar">Sugar</string>
    <string name="take_photo">Take photo</string>
    <style name="Base.Theme.MacroTracker" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorOnError">@color/on_error</item>
    </style>
    <style name="MacroButton" parent="Widget.Material3.Button">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="MacroButtonOutlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="MacroCard" parent="Widget.Material3.CardView.Elevated">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
    </style>
    <style name="Theme.MacroTracker" parent="Base.Theme.MacroTracker"/>
</resources>