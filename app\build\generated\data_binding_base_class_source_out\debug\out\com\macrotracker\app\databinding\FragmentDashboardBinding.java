// Generated by view binder compiler. Do not edit!
package com.macrotracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.macrotracker.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDashboardBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ProgressBar caloriesProgress;

  @NonNull
  public final TextView caloriesText;

  @NonNull
  public final ProgressBar carbsProgress;

  @NonNull
  public final TextView carbsText;

  @NonNull
  public final ProgressBar fatProgress;

  @NonNull
  public final TextView fatText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ProgressBar proteinProgress;

  @NonNull
  public final TextView proteinText;

  @NonNull
  public final Button refreshButton;

  private FragmentDashboardBinding(@NonNull ScrollView rootView,
      @NonNull ProgressBar caloriesProgress, @NonNull TextView caloriesText,
      @NonNull ProgressBar carbsProgress, @NonNull TextView carbsText,
      @NonNull ProgressBar fatProgress, @NonNull TextView fatText, @NonNull ProgressBar progressBar,
      @NonNull ProgressBar proteinProgress, @NonNull TextView proteinText,
      @NonNull Button refreshButton) {
    this.rootView = rootView;
    this.caloriesProgress = caloriesProgress;
    this.caloriesText = caloriesText;
    this.carbsProgress = carbsProgress;
    this.carbsText = carbsText;
    this.fatProgress = fatProgress;
    this.fatText = fatText;
    this.progressBar = progressBar;
    this.proteinProgress = proteinProgress;
    this.proteinText = proteinText;
    this.refreshButton = refreshButton;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.calories_progress;
      ProgressBar caloriesProgress = ViewBindings.findChildViewById(rootView, id);
      if (caloriesProgress == null) {
        break missingId;
      }

      id = R.id.calories_text;
      TextView caloriesText = ViewBindings.findChildViewById(rootView, id);
      if (caloriesText == null) {
        break missingId;
      }

      id = R.id.carbs_progress;
      ProgressBar carbsProgress = ViewBindings.findChildViewById(rootView, id);
      if (carbsProgress == null) {
        break missingId;
      }

      id = R.id.carbs_text;
      TextView carbsText = ViewBindings.findChildViewById(rootView, id);
      if (carbsText == null) {
        break missingId;
      }

      id = R.id.fat_progress;
      ProgressBar fatProgress = ViewBindings.findChildViewById(rootView, id);
      if (fatProgress == null) {
        break missingId;
      }

      id = R.id.fat_text;
      TextView fatText = ViewBindings.findChildViewById(rootView, id);
      if (fatText == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.protein_progress;
      ProgressBar proteinProgress = ViewBindings.findChildViewById(rootView, id);
      if (proteinProgress == null) {
        break missingId;
      }

      id = R.id.protein_text;
      TextView proteinText = ViewBindings.findChildViewById(rootView, id);
      if (proteinText == null) {
        break missingId;
      }

      id = R.id.refresh_button;
      Button refreshButton = ViewBindings.findChildViewById(rootView, id);
      if (refreshButton == null) {
        break missingId;
      }

      return new FragmentDashboardBinding((ScrollView) rootView, caloriesProgress, caloriesText,
          carbsProgress, carbsText, fatProgress, fatText, progressBar, proteinProgress, proteinText,
          refreshButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
