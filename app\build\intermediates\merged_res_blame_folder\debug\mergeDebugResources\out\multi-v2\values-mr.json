{"logs": [{"outputFile": "com.macrotracker.app-mergeDebugResources-2:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5378ea3a25a7f71addffda9b7a578b53\\transformed\\navigation-ui-2.9.3\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16439,16551", "endColumns": "111,116", "endOffsets": "16546,16663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6511df1e446016865fae0e249649412\\transformed\\material-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1046,1109,1200,1265,1324,1412,1474,1536,1596,1663,1726,1780,1894,1951,2012,2066,2136,2255,2336,2413,2502,2584,2669,2804,2881,2958,3099,3185,3269,3325,3377,3443,3513,3591,3662,3744,3814,3890,3961,4030,4144,4240,4314,4412,4508,4582,4652,4754,4809,4897,4964,5051,5144,5207,5271,5334,5400,5500,5609,5703,5810,5870,5926,6004,6088,6166", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "254,339,426,509,602,686,786,902,984,1041,1104,1195,1260,1319,1407,1469,1531,1591,1658,1721,1775,1889,1946,2007,2061,2131,2250,2331,2408,2497,2579,2664,2799,2876,2953,3094,3180,3264,3320,3372,3438,3508,3586,3657,3739,3809,3885,3956,4025,4139,4235,4309,4407,4503,4577,4647,4749,4804,4892,4959,5046,5139,5202,5266,5329,5395,5495,5604,5698,5805,5865,5921,5999,6083,6161,6234"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3072,3157,3244,3327,3420,4236,4336,4452,4992,5049,5112,5203,5439,11749,11837,11899,11961,12021,12088,12151,12205,12319,12376,12437,12491,12561,12768,12849,12926,13015,13097,13182,13317,13394,13471,13612,13698,13782,13838,13890,13956,14026,14104,14175,14257,14327,14403,14474,14543,14657,14753,14827,14925,15021,15095,15165,15267,15322,15410,15477,15564,15657,15720,15784,15847,15913,16013,16122,16216,16323,16383,16840,17163,17247,17396", "endLines": "5,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "304,3152,3239,3322,3415,3499,4331,4447,4529,5044,5107,5198,5263,5493,11832,11894,11956,12016,12083,12146,12200,12314,12371,12432,12486,12556,12675,12844,12921,13010,13092,13177,13312,13389,13466,13607,13693,13777,13833,13885,13951,14021,14099,14170,14252,14322,14398,14469,14538,14652,14748,14822,14920,15016,15090,15160,15262,15317,15405,15472,15559,15652,15715,15779,15842,15908,16008,16117,16211,16318,16378,16434,16913,17242,17320,17464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831512154db5267262fed228477d4e6b\\transformed\\material3-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4687,4772,4862,4962,5042,5125,5224,5323,5420,5519,5606,5710,5810,5914,6032,6112,6212", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4682,4767,4857,4957,5037,5120,5219,5318,5415,5514,5601,5705,5805,5909,6027,6107,6207,6301"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5498,5623,5746,5857,5982,6085,6185,6300,6436,6559,6705,6790,6896,6987,7085,7199,7329,7440,7575,7709,7837,8015,8140,8256,8375,8500,8592,8687,8807,8936,9036,9139,9248,9385,9527,9642,9740,9816,9919,10023,10130,10215,10305,10405,10485,10568,10667,10766,10863,10962,11049,11153,11253,11357,11475,11555,11655", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "5618,5741,5852,5977,6080,6180,6295,6431,6554,6700,6785,6891,6982,7080,7194,7324,7435,7570,7704,7832,8010,8135,8251,8370,8495,8587,8682,8802,8931,9031,9134,9243,9380,9522,9637,9735,9811,9914,10018,10125,10210,10300,10400,10480,10563,10662,10761,10858,10957,11044,11148,11248,11352,11470,11550,11650,11744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f09420083b708841bdec860b0e3e3c1b\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "33,197,198", "startColumns": "4,4,4", "startOffsets": "2999,18142,18227", "endColumns": "72,84,84", "endOffsets": "3067,18222,18307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00d4f69db0bff584c09211a0d3ca791f\\transformed\\material-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12680", "endColumns": "87", "endOffsets": "12763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c9b3062a3a9baebe8ed60ea9c7199b7\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,355,452,550,637,723,808,897,980,1060,1145,1216,1300,1376,1452,1528,1604,1670", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "268,350,447,545,632,718,803,892,975,1055,1140,1211,1295,1371,1447,1523,1599,1665,1783"}, "to": {"startLines": "49,50,51,52,53,58,59,179,180,182,183,187,189,190,191,192,194,195,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4534,4628,4710,4807,4905,5268,5354,16668,16757,16918,16998,17325,17469,17553,17629,17705,17882,17958,18024", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "4623,4705,4802,4900,4987,5349,5434,16752,16835,16993,17078,17391,17548,17624,17700,17776,17953,18019,18137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8794f68105cf4d13d6ceb8339e99e2bd\\transformed\\core-1.16.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3504,3604,3708,3809,3912,4014,4119,17781", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3599,3703,3804,3907,4009,4114,4231,17877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e2ee5ef775fc5fd4089dfad5d6aab731\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,17083", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,17158"}}]}]}