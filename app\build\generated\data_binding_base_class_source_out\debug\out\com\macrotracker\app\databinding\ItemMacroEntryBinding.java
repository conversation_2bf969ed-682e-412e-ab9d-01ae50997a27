// Generated by view binder compiler. Do not edit!
package com.macrotracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.macrotracker.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMacroEntryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView textCalories;

  @NonNull
  public final TextView textCarbs;

  @NonNull
  public final TextView textEntryMethod;

  @NonNull
  public final TextView textFat;

  @NonNull
  public final TextView textFoodName;

  @NonNull
  public final TextView textProtein;

  @NonNull
  public final TextView textTimestamp;

  private ItemMacroEntryBinding(@NonNull MaterialCardView rootView, @NonNull TextView textCalories,
      @NonNull TextView textCarbs, @NonNull TextView textEntryMethod, @NonNull TextView textFat,
      @NonNull TextView textFoodName, @NonNull TextView textProtein,
      @NonNull TextView textTimestamp) {
    this.rootView = rootView;
    this.textCalories = textCalories;
    this.textCarbs = textCarbs;
    this.textEntryMethod = textEntryMethod;
    this.textFat = textFat;
    this.textFoodName = textFoodName;
    this.textProtein = textProtein;
    this.textTimestamp = textTimestamp;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMacroEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMacroEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_macro_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMacroEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.text_calories;
      TextView textCalories = ViewBindings.findChildViewById(rootView, id);
      if (textCalories == null) {
        break missingId;
      }

      id = R.id.text_carbs;
      TextView textCarbs = ViewBindings.findChildViewById(rootView, id);
      if (textCarbs == null) {
        break missingId;
      }

      id = R.id.text_entry_method;
      TextView textEntryMethod = ViewBindings.findChildViewById(rootView, id);
      if (textEntryMethod == null) {
        break missingId;
      }

      id = R.id.text_fat;
      TextView textFat = ViewBindings.findChildViewById(rootView, id);
      if (textFat == null) {
        break missingId;
      }

      id = R.id.text_food_name;
      TextView textFoodName = ViewBindings.findChildViewById(rootView, id);
      if (textFoodName == null) {
        break missingId;
      }

      id = R.id.text_protein;
      TextView textProtein = ViewBindings.findChildViewById(rootView, id);
      if (textProtein == null) {
        break missingId;
      }

      id = R.id.text_timestamp;
      TextView textTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (textTimestamp == null) {
        break missingId;
      }

      return new ItemMacroEntryBinding((MaterialCardView) rootView, textCalories, textCarbs,
          textEntryMethod, textFat, textFoodName, textProtein, textTimestamp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
