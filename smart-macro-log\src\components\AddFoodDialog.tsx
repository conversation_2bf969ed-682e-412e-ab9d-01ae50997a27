import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Plus, Mic, Camera, Type, Loader2 } from "lucide-react";
import { FoodEntry, MacroData } from "@/types/nutrition";
import { toast } from "sonner";

interface AddFoodDialogProps {
  onAddFood: (entry: Omit<FoodEntry, 'id' | 'timestamp'>) => void;
}

export function AddFoodDialog({ onAddFood }: AddFoodDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [textInput, setTextInput] = useState("");
  const [isListening, setIsListening] = useState(false);

  const analyzeFood = async (description: string, method: 'text' | 'voice' | 'image'): Promise<{ name: string; quantity: string; macros: MacroData }> => {
    setLoading(true);
    try {
      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyCf_mH39uXUNaTHrbk0WVrGj3LPzo_73AU', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `Analyze this food description and provide nutritional information: "${description}". 
              Return ONLY a JSON object in this exact format:
              {
                "name": "food name",
                "quantity": "amount (e.g., 1 cup, 100g)",
                "macros": {
                  "calories": number,
                  "protein": number,
                  "carbs": number,
                  "fats": number
                }
              }
              Be specific about quantity and provide realistic macro values.`
            }]
          }]
        }),
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response from Gemini API');
      }

      const text = data.candidates[0].content.parts[0].text;
      
      // Extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');
      
      const result = JSON.parse(jsonMatch[0]);
      
      // Validate the result has required properties
      if (!result.name || !result.quantity || !result.macros) {
        throw new Error('Invalid response format from API');
      }
      
      return result;
    } catch (error) {
      console.error('Error analyzing food:', error);
      toast.error(`Failed to analyze food: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Enhanced fallback with better estimates based on common foods
      const fallbackMacros = getFallbackMacros(description);
      
      return {
        name: description,
        quantity: "1 serving",
        macros: fallbackMacros
      };
    } finally {
      setLoading(false);
    }
  };

  const handleTextSubmit = async () => {
    if (!textInput.trim()) return;
    
    const result = await analyzeFood(textInput, 'text');
    onAddFood({
      ...result,
      method: 'text'
    });
    
    setTextInput("");
    setOpen(false);
    toast.success("Food added successfully!");
  };

  const getFallbackMacros = (description: string): MacroData => {
    const lower = description.toLowerCase();
    
    // Basic pattern matching for common foods
    if (lower.includes('chicken') || lower.includes('turkey')) {
      return { calories: 165, protein: 31, carbs: 0, fats: 3.6 };
    } else if (lower.includes('rice')) {
      return { calories: 130, protein: 2.7, carbs: 28, fats: 0.3 };
    } else if (lower.includes('oats') || lower.includes('oatmeal')) {
      return { calories: 389, protein: 16.9, carbs: 66, fats: 6.9 };
    } else if (lower.includes('peanut butter')) {
      return { calories: 588, protein: 25, carbs: 20, fats: 50 };
    } else if (lower.includes('milk')) {
      return { calories: 42, protein: 3.4, carbs: 5, fats: 1 };
    } else if (lower.includes('egg')) {
      return { calories: 155, protein: 13, carbs: 1.1, fats: 11 };
    } else if (lower.includes('bread')) {
      return { calories: 265, protein: 9, carbs: 49, fats: 3.2 };
    } else {
      // Generic fallback
      return { calories: 200, protein: 10, carbs: 25, fats: 8 };
    }
  };

  const handleVoiceInput = async () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      toast.error("Speech recognition not supported in this browser");
      return;
    }

    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsListening(true);
    };

    recognition.onresult = async (event) => {
      const transcript = event.results[0][0].transcript;
      setIsListening(false);
      
      const result = await analyzeFood(transcript, 'voice');
      onAddFood({
        ...result,
        method: 'voice'
      });
      
      setOpen(false);
      toast.success("Food added via voice!");
    };

    recognition.onerror = () => {
      setIsListening(false);
      toast.error("Voice recognition failed");
    };

    recognition.start();
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // For demo purposes, we'll simulate image analysis
    toast.success("Image analysis feature coming soon!");
    
    // Placeholder implementation
    const result = await analyzeFood("Image of food", 'image');
    onAddFood({
      ...result,
      name: "Food from image",
      method: 'image'
    });
    
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="w-full" size="lg">
          <Plus className="w-5 h-5 mr-2" />
          Add Food
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Food Entry</DialogTitle>
          <DialogDescription>
            Analyze your food using text, voice, or image input to track macros automatically.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="text" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="text" className="flex items-center gap-2">
              <Type className="w-4 h-4" />
              Type
            </TabsTrigger>
            <TabsTrigger value="voice" className="flex items-center gap-2">
              <Mic className="w-4 h-4" />
              Voice
            </TabsTrigger>
            <TabsTrigger value="image" className="flex items-center gap-2">
              <Camera className="w-4 h-4" />
              Image
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="text" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="food-description">Describe your food</Label>
              <Textarea
                id="food-description"
                placeholder="e.g., 1 cup of brown rice with grilled chicken breast"
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                rows={3}
              />
            </div>
            <Button onClick={handleTextSubmit} className="w-full" disabled={loading || !textInput.trim()}>
              {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Analyze Food
            </Button>
          </TabsContent>
          
          <TabsContent value="voice" className="space-y-4">
            <div className="text-center space-y-4">
              <div className="text-sm text-muted-foreground">
                Tap the button and describe your food
              </div>
              <Button 
                onClick={handleVoiceInput} 
                className="w-full" 
                variant={isListening ? "destructive" : "default"}
                disabled={loading}
              >
                {isListening ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Listening...
                  </>
                ) : (
                  <>
                    <Mic className="w-4 h-4 mr-2" />
                    Start Voice Input
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="image" className="space-y-4">
            <div className="text-center space-y-4">
              <div className="text-sm text-muted-foreground">
                Upload a photo of your food
              </div>
              <Input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="cursor-pointer"
                disabled={loading}
              />
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}