#Sat Aug 09 13:00:32 IST 2025
com.macrotracker.app-main-6\:/drawable/bg_entry_method.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_entry_method.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_add_food.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_food.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_camera.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_camera.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_dashboard.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dashboard.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_edit.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_history.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_launcher_foreground.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_mic.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_mic.xml.flat
com.macrotracker.app-main-6\:/drawable/ic_settings.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.macrotracker.app-main-6\:/menu/bottom_navigation_menu.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
com.macrotracker.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.macrotracker.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.macrotracker.app-main-6\:/mipmap-hdpi/ic_launcher.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.macrotracker.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.macrotracker.app-main-6\:/mipmap-mdpi/ic_launcher.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.macrotracker.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.macrotracker.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.macrotracker.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.macrotracker.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.macrotracker.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.macrotracker.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.macrotracker.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.macrotracker.app-main-6\:/xml/backup_rules.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.macrotracker.app-main-6\:/xml/data_extraction_rules.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.macrotracker.app-main-6\:/xml/file_paths.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.macrotracker.app-mergeDebugResources-3\:/layout/activity_main.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.macrotracker.app-mergeDebugResources-3\:/layout/fragment_dashboard.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_dashboard.xml.flat
com.macrotracker.app-mergeDebugResources-3\:/layout/fragment_food_entry.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_food_entry.xml.flat
com.macrotracker.app-mergeDebugResources-3\:/layout/fragment_history.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history.xml.flat
com.macrotracker.app-mergeDebugResources-3\:/layout/fragment_settings.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_settings.xml.flat
com.macrotracker.app-mergeDebugResources-3\:/layout/item_macro_entry.xml=C\:\\MyCal\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_macro_entry.xml.flat
