<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_food_entry" modulePackage="com.macrotracker.app" filePath="app\src\main\res\layout\fragment_food_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_food_entry_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="103" endOffset="12"/></Target><Target id="@+id/text_view_instructions" view="TextView"><Expressions/><location startLine="21" startOffset="8" endLine="27" endOffset="48"/></Target><Target id="@+id/button_manual_entry" view="Button"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="43"/></Target><Target id="@+id/button_speech_entry" view="Button"><Expressions/><location startLine="39" startOffset="8" endLine="46" endOffset="43"/></Target><Target id="@+id/button_image_entry" view="Button"><Expressions/><location startLine="48" startOffset="8" endLine="55" endOffset="43"/></Target><Target id="@+id/edit_text_food" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="65" startOffset="12" endLine="71" endOffset="38"/></Target><Target id="@+id/button_add_food" view="Button"><Expressions/><location startLine="75" startOffset="8" endLine="82" endOffset="39"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="84" startOffset="8" endLine="90" endOffset="39"/></Target><Target id="@+id/text_view_status" view="TextView"><Expressions/><location startLine="92" startOffset="8" endLine="99" endOffset="44"/></Target></Targets></Layout>