package com.macrotracker.app.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.macrotracker.app.data.entity.DailyGoals

@Dao
interface DailyGoalsDao {
    
    @Query("SELECT * FROM daily_goals WHERE date = :date")
    suspend fun getGoalsForDate(date: String): DailyGoals?
    
    @Query("SELECT * FROM daily_goals WHERE date = :date")
    fun getGoalsForDateLive(date: String): LiveData<DailyGoals?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateGoals(goals: DailyGoals)
    
    @Query("SELECT * FROM daily_goals ORDER BY date DESC LIMIT 1")
    suspend fun getLatestGoals(): DailyGoals?
    
    @Delete
    suspend fun deleteGoals(goals: DailyGoals)
}
