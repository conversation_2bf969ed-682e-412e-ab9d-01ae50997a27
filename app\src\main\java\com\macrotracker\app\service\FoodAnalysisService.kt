package com.macrotracker.app.service

import android.graphics.Bitmap
import android.util.Base64
import com.google.gson.Gson
import com.macrotracker.app.api.*
import com.macrotracker.app.data.entity.MacroEntry
import com.macrotracker.app.data.entity.EntryMethod
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.ByteArrayOutputStream
import java.util.*
/**
 * Food Analysis Service using Google Gemini 2.5 Flash
 *
 * This service provides AI-powered food analysis capabilities including:
 * - Text-based food description analysis
 * - Image-based food recognition and portion estimation
 * - Nutritional information extraction with improved accuracy
 *
 * Uses Gemini 2.5 Flash model for optimal cost-performance balance
 * and enhanced multimodal capabilities.
 */
class FoodAnalysisService {

    private val apiKey = "AIzaSyCf_mH39uXUNaTHrbk0WVrGj3LPzo_73AU"
    private val baseUrl = "https://generativelanguage.googleapis.com/"
    
    private val retrofit = Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(
            OkHttpClient.Builder()
                .addInterceptor(HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                })
                .build()
        )
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    private val apiService = retrofit.create(GeminiApiService::class.java)
    
    suspend fun analyzeFoodText(foodDescription: String): Result<MacroEntry> {
        return try {
            val prompt = createFoodAnalysisPrompt(foodDescription)
            val request = GeminiRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(text = prompt))
                    )
                )
            )
            
            val response = apiService.generateContent(apiKey, request)
            
            if (response.isSuccessful && response.body() != null) {
                val geminiResponse = response.body()!!
                val responseText = geminiResponse.candidates.firstOrNull()?.content?.parts?.firstOrNull()?.text
                
                if (responseText != null) {
                    parseNutritionResponse(responseText, foodDescription, EntryMethod.MANUAL)
                } else {
                    Result.failure(Exception("No response from Gemini API"))
                }
            } else {
                Result.failure(Exception("API call failed: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun analyzeFoodImage(bitmap: Bitmap, description: String = ""): Result<MacroEntry> {
        return try {
            val base64Image = bitmapToBase64(bitmap)
            val prompt = createImageAnalysisPrompt(description)
            
            val request = GeminiRequest(
                contents = listOf(
                    Content(
                        parts = listOf(
                            Part(text = prompt),
                            Part(
                                inlineData = InlineData(
                                    mimeType = "image/jpeg",
                                    data = base64Image
                                )
                            )
                        )
                    )
                )
            )
            
            val response = apiService.generateContent(apiKey, request)
            
            if (response.isSuccessful && response.body() != null) {
                val geminiResponse = response.body()!!
                val responseText = geminiResponse.candidates.firstOrNull()?.content?.parts?.firstOrNull()?.text
                
                if (responseText != null) {
                    parseNutritionResponse(responseText, "Food from image", EntryMethod.IMAGE)
                } else {
                    Result.failure(Exception("No response from Gemini API"))
                }
            } else {
                Result.failure(Exception("API call failed: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun createFoodAnalysisPrompt(foodDescription: String): String {
        return """
            You are a nutrition expert. Analyze the following food description and provide accurate nutritional information.

            Food: "$foodDescription"

            Provide the nutritional information in this exact JSON format:
            {
                "foodName": "descriptive name of the food",
                "quantity": "quantity and unit (e.g., '1 cup', '100g', '1 medium')",
                "calories": number,
                "protein": number,
                "carbs": number,
                "fat": number,
                "fiber": number,
                "sugar": number,
                "sodium": number
            }

            Guidelines:
            - All macro values should be in grams except sodium (in milligrams)
            - Calories should be a whole number
            - Use standard USDA nutrition database values when possible
            - For ambiguous quantities, assume standard serving sizes
            - If you can't determine exact values, provide reasonable estimates based on similar foods
            - Only respond with valid JSON, no additional text or explanations
        """.trimIndent()
    }
    
    private fun createImageAnalysisPrompt(additionalDescription: String): String {
        val basePrompt = """
            You are a nutrition expert with expertise in food recognition and portion estimation. Analyze this food image carefully.

            Tasks:
            1. Identify all visible food items in the image
            2. Estimate the portion sizes based on visual cues (plate size, utensils, etc.)
            3. Provide accurate nutritional information for the total food shown

            Provide the nutritional information in this exact JSON format:
            {
                "foodName": "descriptive name of the food items (e.g., 'Grilled chicken breast with steamed broccoli and rice')",
                "quantity": "estimated total quantity and description (e.g., '1 medium serving', '200g chicken + 150g vegetables')",
                "calories": number,
                "protein": number,
                "carbs": number,
                "fat": number,
                "fiber": number,
                "sugar": number,
                "sodium": number
            }

            Guidelines:
            - All macro values should be in grams except sodium (in milligrams)
            - Calories should be a whole number
            - Use visual portion estimation techniques (compare to common objects, plate coverage, etc.)
            - Consider cooking methods that might affect nutritional content
            - If multiple food items are present, provide combined nutritional values
            - Use standard USDA nutrition database values when possible
            - Only respond with valid JSON, no additional text or explanations
        """.trimIndent()

        return if (additionalDescription.isNotBlank()) {
            "$basePrompt\n\nAdditional context from user: $additionalDescription"
        } else {
            basePrompt
        }
    }
    
    private fun parseNutritionResponse(responseText: String, originalDescription: String, method: EntryMethod): Result<MacroEntry> {
        return try {
            // Extract JSON from response (in case there's extra text)
            val jsonStart = responseText.indexOf("{")
            val jsonEnd = responseText.lastIndexOf("}") + 1
            
            if (jsonStart == -1 || jsonEnd <= jsonStart) {
                return Result.failure(Exception("No valid JSON found in response"))
            }
            
            val jsonString = responseText.substring(jsonStart, jsonEnd)
            val gson = Gson()
            val nutritionData = gson.fromJson(jsonString, NutritionData::class.java)
            
            val macroEntry = MacroEntry(
                foodName = nutritionData.foodName ?: originalDescription,
                quantity = nutritionData.quantity ?: "1 serving",
                calories = nutritionData.calories ?: 0.0,
                protein = nutritionData.protein ?: 0.0,
                carbs = nutritionData.carbs ?: 0.0,
                fat = nutritionData.fat ?: 0.0,
                fiber = nutritionData.fiber ?: 0.0,
                sugar = nutritionData.sugar ?: 0.0,
                sodium = nutritionData.sodium ?: 0.0,
                timestamp = Date(),
                entryMethod = method
            )
            
            Result.success(macroEntry)
        } catch (e: Exception) {
            Result.failure(Exception("Failed to parse nutrition data: ${e.message}"))
        }
    }
    
    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.NO_WRAP)
    }
}

data class NutritionData(
    val foodName: String?,
    val quantity: String?,
    val calories: Double?,
    val protein: Double?,
    val carbs: Double?,
    val fat: Double?,
    val fiber: Double?,
    val sugar: Double?,
    val sodium: Double?
)
