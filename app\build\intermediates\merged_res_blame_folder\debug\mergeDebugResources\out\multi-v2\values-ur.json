{"logs": [{"outputFile": "com.macrotracker.app-mergeDebugResources-2:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5378ea3a25a7f71addffda9b7a578b53\\transformed\\navigation-ui-2.9.3\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,114", "endOffsets": "162,277"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16545,16657", "endColumns": "111,114", "endOffsets": "16652,16767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c9b3062a3a9baebe8ed60ea9c7199b7\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,356,446,543,631,712,805,893,979,1062,1147,1222,1305,1383,1457,1530,1605,1671", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "268,351,441,538,626,707,800,888,974,1057,1142,1217,1300,1378,1452,1525,1600,1666,1783"}, "to": {"startLines": "49,50,51,52,53,58,59,179,180,182,183,187,189,190,191,192,194,195,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4553,4647,4730,4820,4917,5290,5371,16772,16860,17026,17109,17446,17595,17678,17756,17830,18004,18079,18145", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "4642,4725,4815,4912,5000,5366,5459,16855,16941,17104,17189,17516,17673,17751,17825,17898,18074,18140,18257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831512154db5267262fed228477d4e6b\\transformed\\material3-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4733,4823,4914,5029,5109,5194,5297,5403,5500,5603,5688,5794,5893,5996,6117,6197,6299", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4728,4818,4909,5024,5104,5189,5292,5398,5495,5598,5683,5789,5888,5991,6112,6192,6294,6388"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5523,5641,5763,5879,5997,6095,6192,6307,6442,6566,6706,6791,6895,6991,7091,7208,7338,7447,7591,7734,7863,8061,8186,8305,8428,8566,8663,8758,8882,9006,9107,9212,9318,9461,9610,9716,9820,9896,9992,10089,10201,10291,10382,10497,10577,10662,10765,10871,10968,11071,11156,11262,11361,11464,11585,11665,11767", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "5636,5758,5874,5992,6090,6187,6302,6437,6561,6701,6786,6890,6986,7086,7203,7333,7442,7586,7729,7858,8056,8181,8300,8423,8561,8658,8753,8877,9001,9102,9207,9313,9456,9605,9711,9815,9891,9987,10084,10196,10286,10377,10492,10572,10657,10760,10866,10963,11066,11151,11257,11356,11459,11580,11660,11762,11856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00d4f69db0bff584c09211a0d3ca791f\\transformed\\material-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12818", "endColumns": "86", "endOffsets": "12900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f09420083b708841bdec860b0e3e3c1b\\transformed\\foundation-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,86", "endOffsets": "123,208,295"}, "to": {"startLines": "33,197,198", "startColumns": "4,4,4", "startOffsets": "3036,18262,18347", "endColumns": "72,84,86", "endOffsets": "3104,18342,18429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8794f68105cf4d13d6ceb8339e99e2bd\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3530,3628,3730,3832,3936,4039,4137,17903", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3623,3725,3827,3931,4034,4132,4246,17999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6511df1e446016865fae0e249649412\\transformed\\material-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1038,1103,1197,1267,1326,1416,1480,1549,1607,1676,1736,1800,1912,1971,2030,2085,2160,2283,2363,2446,2540,2627,2711,2844,2926,3007,3138,3225,3307,3365,3421,3487,3562,3642,3713,3792,3859,3934,4011,4075,4182,4276,4346,4435,4528,4602,4677,4767,4823,4902,4969,5053,5137,5199,5263,5326,5392,5492,5599,5693,5801,5863,5923,6003,6088,6169", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "254,332,410,488,586,675,775,894,977,1033,1098,1192,1262,1321,1411,1475,1544,1602,1671,1731,1795,1907,1966,2025,2080,2155,2278,2358,2441,2535,2622,2706,2839,2921,3002,3133,3220,3302,3360,3416,3482,3557,3637,3708,3787,3854,3929,4006,4070,4177,4271,4341,4430,4523,4597,4672,4762,4818,4897,4964,5048,5132,5194,5258,5321,5387,5487,5594,5688,5796,5858,5918,5998,6083,6164,6238"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3109,3187,3265,3343,3441,4251,4351,4470,5005,5061,5126,5220,5464,11861,11951,12015,12084,12142,12211,12271,12335,12447,12506,12565,12620,12695,12905,12985,13068,13162,13249,13333,13466,13548,13629,13760,13847,13929,13987,14043,14109,14184,14264,14335,14414,14481,14556,14633,14697,14804,14898,14968,15057,15150,15224,15299,15389,15445,15524,15591,15675,15759,15821,15885,15948,16014,16114,16221,16315,16423,16485,16946,17280,17365,17521", "endLines": "5,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "304,3182,3260,3338,3436,3525,4346,4465,4548,5056,5121,5215,5285,5518,11946,12010,12079,12137,12206,12266,12330,12442,12501,12560,12615,12690,12813,12980,13063,13157,13244,13328,13461,13543,13624,13755,13842,13924,13982,14038,14104,14179,14259,14330,14409,14476,14551,14628,14692,14799,14893,14963,15052,15145,15219,15294,15384,15440,15519,15586,15670,15754,15816,15880,15943,16009,16109,16216,16310,16418,16480,16540,17021,17360,17441,17590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e2ee5ef775fc5fd4089dfad5d6aab731\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,17194", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,17275"}}]}]}