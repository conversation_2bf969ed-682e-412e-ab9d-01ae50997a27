import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { MacroProgress } from "@/components/MacroProgress";
import { FoodEntryCard } from "@/components/FoodEntryCard";
import { AddFoodDialog } from "@/components/AddFoodDialog";
import { GoalsSettingsDialog } from "@/components/GoalsSettingsDialog";
import { FoodEntry, MacroData, DailyGoals } from "@/types/nutrition";
import { Activity, Target } from "lucide-react";

const Index = () => {
  const [foodEntries, setFoodEntries] = useState<FoodEntry[]>([]);
  const [dailyGoals, setDailyGoals] = useState<DailyGoals>({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fats: 67
  });

  // Load data from localStorage
  useEffect(() => {
    const savedEntries = localStorage.getItem('macroTracker-entries');
    if (savedEntries) {
      const entries = JSON.parse(savedEntries).map((entry: any) => ({
        ...entry,
        timestamp: new Date(entry.timestamp)
      }));
      setFoodEntries(entries);
    }

    const savedGoals = localStorage.getItem('macroTracker-goals');
    if (savedGoals) {
      setDailyGoals(JSON.parse(savedGoals));
    }
  }, []);

  // Save entries to localStorage
  useEffect(() => {
    localStorage.setItem('macroTracker-entries', JSON.stringify(foodEntries));
  }, [foodEntries]);

  // Save goals to localStorage
  useEffect(() => {
    localStorage.setItem('macroTracker-goals', JSON.stringify(dailyGoals));
  }, [dailyGoals]);

  const updateDailyGoals = (newGoals: DailyGoals) => {
    setDailyGoals(newGoals);
  };

  const addFoodEntry = (entry: Omit<FoodEntry, 'id' | 'timestamp'>) => {
    const newEntry: FoodEntry = {
      ...entry,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    setFoodEntries(prev => [newEntry, ...prev]);
  };

  // Calculate today's total macros
  const today = new Date().toDateString();
  const todayEntries = foodEntries.filter(entry => 
    entry.timestamp.toDateString() === today
  );

  const currentMacros: MacroData = todayEntries.reduce(
    (total, entry) => ({
      calories: total.calories + entry.macros.calories,
      protein: total.protein + entry.macros.protein,
      carbs: total.carbs + entry.macros.carbs,
      fats: total.fats + entry.macros.fats,
    }),
    { calories: 0, protein: 0, carbs: 0, fats: 0 }
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-background/80 backdrop-blur-md border-b">
        <div className="container max-w-md mx-auto p-4">
          <h1 className="text-2xl font-bold text-foreground">MacroTracker</h1>
          <p className="text-sm text-muted-foreground">Track your daily nutrition</p>
        </div>
      </div>

      <div className="container max-w-md mx-auto p-4 space-y-6">
        {/* Daily Progress */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-primary" />
                Today's Progress
              </CardTitle>
              <GoalsSettingsDialog 
                currentGoals={dailyGoals} 
                onUpdateGoals={updateDailyGoals} 
              />
            </div>
          </CardHeader>
          <CardContent>
            <MacroProgress current={currentMacros} goals={dailyGoals} />
          </CardContent>
        </Card>

        {/* Add Food Button */}
        <AddFoodDialog onAddFood={addFoodEntry} />

        {/* Recent Entries */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-primary" />
              Recent Entries
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {todayEntries.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No entries for today</p>
                <p className="text-sm text-muted-foreground mt-1">Add your first meal!</p>
              </div>
            ) : (
              todayEntries.map((entry) => (
                <FoodEntryCard key={entry.id} entry={entry} />
              ))
            )}
          </CardContent>
        </Card>

        {/* Summary Stats */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-primary">{todayEntries.length}</p>
                <p className="text-sm text-muted-foreground">Meals Today</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-primary">
                  {Math.round((currentMacros.calories / dailyGoals.calories) * 100)}%
                </p>
                <p className="text-sm text-muted-foreground">Daily Goal</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;