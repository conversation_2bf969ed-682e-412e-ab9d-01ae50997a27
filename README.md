# Macro Tracker Android App

A comprehensive Android application for tracking daily macronutrients (calories, protein, carbs, fat) using AI-powered food analysis.

## Features

### 🍽️ Multiple Food Entry Methods
- **Manual Text Input**: Type food descriptions like "1 cup rice" or "2 slices bread"
- **Speech-to-Text**: Speak your food intake naturally
- **Image Recognition**: Take photos of your meals for automatic analysis

### 📊 Smart Dashboard
- Real-time macro tracking with progress bars
- Daily calorie and macronutrient visualization
- Goal tracking and progress monitoring

### 🤖 AI-Powered Analysis
- Powered by Google Gemini 2.5 Flash for accurate food recognition
- Advanced automatic macro calculation from text descriptions
- Intelligent image analysis for portion estimation
- Improved accuracy and faster response times

### 📱 Modern Android Features
- Material Design 3 UI
- Room database for local storage
- Camera integration with proper permissions
- Speech recognition support

## Technical Stack

- **Language**: Kotlin
- **UI Framework**: Android Views with ViewBinding
- **Database**: Room (SQLite)
- **Architecture**: MVVM with Repository pattern
- **AI Integration**: Google Gemini API
- **Camera**: CameraX
- **Speech**: Android Speech Recognition API

## Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd MacroTracker
   ```

2. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the cloned directory

3. **API Configuration**
   - The Gemini API key is already configured in the app
   - For production use, consider moving it to a secure configuration

4. **Build and Run**
   - Connect an Android device or start an emulator
   - Click "Run" in Android Studio

## App Structure

```
app/src/main/java/com/macrotracker/app/
├── data/
│   ├── entity/          # Room entities (MacroEntry, DailyGoals)
│   ├── dao/             # Data Access Objects
│   ├── database/        # Room database configuration
│   └── repository/      # Repository pattern implementation
├── service/
│   ├── FoodAnalysisService.kt    # Gemini API integration
│   ├── SpeechRecognitionService.kt
│   └── CameraService.kt
├── ui/
│   ├── dashboard/       # Dashboard fragment and ViewModel
│   ├── foodentry/       # Food entry fragment and ViewModel
│   ├── history/         # History fragment and ViewModel
│   └── settings/        # Settings fragment and ViewModel
└── util/                # Utility classes
```

## Permissions Required

- **CAMERA**: For taking photos of food
- **RECORD_AUDIO**: For speech-to-text functionality
- **INTERNET**: For Gemini API calls
- **WRITE_EXTERNAL_STORAGE**: For temporary image storage (API < 29)

## Usage Guide

### Adding Food Entries

1. **Manual Entry**:
   - Tap "Add Food" in bottom navigation
   - Select "Enter food manually"
   - Type description like "1 medium apple" or "200g chicken breast"
   - Tap "Add Food"

2. **Speech Entry**:
   - Tap "Speak food"
   - Grant microphone permission if prompted
   - Speak your food description clearly
   - The app will automatically process and save the entry

3. **Image Entry**:
   - Tap "Take photo"
   - Grant camera permission if prompted
   - Take a photo of your food
   - The AI will analyze and extract macro information

### Viewing Progress

- **Dashboard**: View daily macro progress with visual progress bars
- **History**: See all your food entries with timestamps and macro breakdown
- **Settings**: Set and adjust your daily macro goals

## API Integration

The app uses Google Gemini 2.5 Flash for advanced food analysis:
- Text descriptions are processed with improved accuracy to extract nutritional information
- Images are analyzed using advanced computer vision to identify food items and estimate portions
- Unified model handles both text and image inputs efficiently
- All responses are parsed into structured macro data with better reliability

## Database Schema

### MacroEntry
- Food name and quantity
- Macro values (calories, protein, carbs, fat, fiber, sugar, sodium)
- Entry method (manual, speech, image)
- Timestamp

### DailyGoals
- Date-specific macro goals
- Customizable targets for each macro

## Future Enhancements

- Barcode scanning for packaged foods
- Meal planning and recipes
- Export data functionality
- Social sharing features
- Offline food database
- Advanced analytics and trends

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational and personal use. Please ensure you comply with Google Gemini API terms of service when using the AI features.
