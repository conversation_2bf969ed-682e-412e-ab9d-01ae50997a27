[{"merged": "com.macrotracker.app-debug-4:/xml_data_extraction_rules.xml.flat", "source": "com.macrotracker.app-main-6:/xml/data_extraction_rules.xml"}, {"merged": "com.macrotracker.app-debug-4:/layout_fragment_settings.xml.flat", "source": "com.macrotracker.app-main-6:/layout/fragment_settings.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_add_food.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_add_food.xml"}, {"merged": "com.macrotracker.app-debug-4:/xml_backup_rules.xml.flat", "source": "com.macrotracker.app-main-6:/xml/backup_rules.xml"}, {"merged": "com.macrotracker.app-debug-4:/layout_fragment_food_entry.xml.flat", "source": "com.macrotracker.app-main-6:/layout/fragment_food_entry.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_settings.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_settings.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_edit.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_edit.xml"}, {"merged": "com.macrotracker.app-debug-4:/xml_file_paths.xml.flat", "source": "com.macrotracker.app-main-6:/xml/file_paths.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_dashboard.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_dashboard.xml"}, {"merged": "com.macrotracker.app-debug-4:/layout_fragment_dashboard.xml.flat", "source": "com.macrotracker.app-main-6:/layout/fragment_dashboard.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_history.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_history.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_mic.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_mic.xml"}, {"merged": "com.macrotracker.app-debug-4:/menu_bottom_navigation_menu.xml.flat", "source": "com.macrotracker.app-main-6:/menu/bottom_navigation_menu.xml"}, {"merged": "com.macrotracker.app-debug-4:/layout_fragment_history.xml.flat", "source": "com.macrotracker.app-main-6:/layout/fragment_history.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_ic_camera.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/ic_camera.xml"}, {"merged": "com.macrotracker.app-debug-4:/layout_activity_main.xml.flat", "source": "com.macrotracker.app-main-6:/layout/activity_main.xml"}, {"merged": "com.macrotracker.app-debug-4:/layout_item_macro_entry.xml.flat", "source": "com.macrotracker.app-main-6:/layout/item_macro_entry.xml"}, {"merged": "com.macrotracker.app-debug-4:/drawable_bg_entry_method.xml.flat", "source": "com.macrotracker.app-main-6:/drawable/bg_entry_method.xml"}]