{"logs": [{"outputFile": "com.macrotracker.app-mergeDebugResources-2:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6511df1e446016865fae0e249649412\\transformed\\material-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1017,1082,1171,1236,1295,1381,1445,1509,1572,1645,1709,1763,1875,1933,1995,2049,2121,2243,2330,2406,2498,2580,2666,2806,2883,2964,3091,3182,3259,3313,3364,3430,3500,3577,3648,3723,3794,3871,3940,4009,4116,4207,4279,4368,4457,4531,4603,4689,4739,4818,4884,4964,5048,5110,5174,5237,5306,5406,5501,5593,5685,5743,5798,5882,5963,6038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "267,349,427,504,590,674,768,873,952,1012,1077,1166,1231,1290,1376,1440,1504,1567,1640,1704,1758,1870,1928,1990,2044,2116,2238,2325,2401,2493,2575,2661,2801,2878,2959,3086,3177,3254,3308,3359,3425,3495,3572,3643,3718,3789,3866,3935,4004,4111,4202,4274,4363,4452,4526,4598,4684,4734,4813,4879,4959,5043,5105,5169,5232,5301,5401,5496,5588,5680,5738,5793,5877,5958,6033,6108"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3159,3241,3319,3396,3482,4301,4395,4500,5041,5101,5166,5255,5495,11916,12002,12066,12130,12193,12266,12330,12384,12496,12554,12616,12670,12742,12952,13039,13115,13207,13289,13375,13515,13592,13673,13800,13891,13968,14022,14073,14139,14209,14286,14357,14432,14503,14580,14649,14718,14825,14916,14988,15077,15166,15240,15312,15398,15448,15527,15593,15673,15757,15819,15883,15946,16015,16115,16210,16302,16394,16452,16920,17254,17335,17480", "endLines": "5,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "317,3236,3314,3391,3477,3561,4390,4495,4574,5096,5161,5250,5315,5549,11997,12061,12125,12188,12261,12325,12379,12491,12549,12611,12665,12737,12859,13034,13110,13202,13284,13370,13510,13587,13668,13795,13886,13963,14017,14068,14134,14204,14281,14352,14427,14498,14575,14644,14713,14820,14911,14983,15072,15161,15235,15307,15393,15443,15522,15588,15668,15752,15814,15878,15941,16010,16110,16205,16297,16389,16447,16502,16999,17330,17405,17550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831512154db5267262fed228477d4e6b\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4763,4857,4948,5053,5133,5218,5319,5425,5518,5619,5706,5814,5913,6016,6140,6220,6323", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4758,4852,4943,5048,5128,5213,5314,5420,5513,5614,5701,5809,5908,6011,6135,6215,6318,6412"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5554,5676,5793,5906,6025,6119,6219,6336,6479,6605,6756,6841,6946,7042,7137,7253,7383,7493,7636,7774,7905,8097,8223,8352,8487,8617,8714,8810,8927,9049,9154,9259,9362,9504,9654,9761,9870,9945,10049,10151,10262,10356,10447,10552,10632,10717,10818,10924,11017,11118,11205,11313,11412,11515,11639,11719,11822", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "5671,5788,5901,6020,6114,6214,6331,6474,6600,6751,6836,6941,7037,7132,7248,7378,7488,7631,7769,7900,8092,8218,8347,8482,8612,8709,8805,8922,9044,9149,9254,9357,9499,9649,9756,9865,9940,10044,10146,10257,10351,10442,10547,10627,10712,10813,10919,11012,11113,11200,11308,11407,11510,11634,11714,11817,11911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00d4f69db0bff584c09211a0d3ca791f\\transformed\\material-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12864", "endColumns": "87", "endOffsets": "12947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8794f68105cf4d13d6ceb8339e99e2bd\\transformed\\core-1.16.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3566,3669,3771,3874,3979,4080,4182,17867", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3664,3766,3869,3974,4075,4177,4296,17963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c9b3062a3a9baebe8ed60ea9c7199b7\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,276,363,455,555,641,718,816,904,991,1069,1151,1221,1305,1380,1457,1533,1616,1683", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,83,74,76,75,82,66,118", "endOffsets": "271,358,450,550,636,713,811,899,986,1064,1146,1216,1300,1375,1452,1528,1611,1678,1797"}, "to": {"startLines": "49,50,51,52,53,58,59,179,180,182,183,187,189,190,191,192,194,195,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4579,4676,4763,4855,4955,5320,5397,16745,16833,17004,17082,17410,17555,17639,17714,17791,17968,18051,18118", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,83,74,76,75,82,66,118", "endOffsets": "4671,4758,4850,4950,5036,5392,5490,16828,16915,17077,17159,17475,17634,17709,17786,17862,18046,18113,18232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5378ea3a25a7f71addffda9b7a578b53\\transformed\\navigation-ui-2.9.3\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,120", "endOffsets": "167,288"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16507,16624", "endColumns": "116,120", "endOffsets": "16619,16740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e2ee5ef775fc5fd4089dfad5d6aab731\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,441,551,658,744,848,968,1047,1128,1219,1312,1415,1510,1610,1703,1798,1894,1985,2075,2164,2274,2378,2484,2595,2699,2817,2980,17164", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "436,546,653,739,843,963,1042,1123,1214,1307,1410,1505,1605,1698,1793,1889,1980,2070,2159,2269,2373,2479,2590,2694,2812,2975,3081,17249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f09420083b708841bdec860b0e3e3c1b\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,87", "endOffsets": "123,208,296"}, "to": {"startLines": "33,197,198", "startColumns": "4,4,4", "startOffsets": "3086,18237,18322", "endColumns": "72,84,87", "endOffsets": "3154,18317,18405"}}]}]}