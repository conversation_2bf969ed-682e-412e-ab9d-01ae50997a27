<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.squareup.okhttp3:okhttp-android:5.1.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\assets"><file name="PublicSuffixDatabase.list" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\assets\PublicSuffixDatabase.list"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\MyCal\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>