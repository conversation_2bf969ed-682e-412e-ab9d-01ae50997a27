R_DEF: Internal format may change without notice
local
color background
color black
color calories_color
color carbs_color
color error
color fat_color
color fiber_color
color on_background
color on_error
color on_primary
color on_secondary
color on_surface
color primary
color primary_variant
color progress_background
color progress_high
color progress_low
color progress_medium
color protein_color
color secondary
color secondary_variant
color sodium_color
color sugar_color
color surface
color white
drawable bg_entry_method
drawable ic_add_food
drawable ic_camera
drawable ic_dashboard
drawable ic_edit
drawable ic_history
drawable ic_mic
drawable ic_settings
id bottom_navigation
id button_add_food
id button_image_entry
id button_manual_entry
id button_save_goals
id button_speech_entry
id calories_progress
id calories_text
id carbs_progress
id carbs_text
id edit_text_calories_goal
id edit_text_carbs_goal
id edit_text_fat_goal
id edit_text_food
id edit_text_protein_goal
id fat_progress
id fat_text
id fragment_container
id nav_add_food
id nav_dashboard
id nav_history
id nav_settings
id progress_bar
id protein_progress
id protein_text
id recycler_view_history
id refresh_button
id text_calories
id text_carbs
id text_entry_method
id text_fat
id text_food_name
id text_protein
id text_timestamp
id text_view_empty
id text_view_instructions
id text_view_status
layout activity_main
layout fragment_dashboard
layout fragment_food_entry
layout fragment_history
layout fragment_settings
layout item_macro_entry
menu bottom_navigation_menu
string add_food
string analyzing_food
string app_name
string calories
string calories_goal
string camera_permission_required
string carbs
string carbs_goal
string daily_goals
string daily_macros
string enter_food_manually
string error_api
string error_camera
string error_network
string error_speech_recognition
string fat
string fat_goal
string fiber
string food_name_hint
string grams
string grant_permission
string kcal
string listening
string microphone_permission_required
string milligrams
string nav_add_food
string nav_dashboard
string nav_history
string nav_settings
string protein
string protein_goal
string set_goals
string sodium
string speak_food
string sugar
string take_photo
style Base.Theme.MacroTracker
style MacroButton
style MacroButtonOutlined
style MacroCard
style Theme.MacroTracker
xml backup_rules
xml data_extraction_rules
xml file_paths
