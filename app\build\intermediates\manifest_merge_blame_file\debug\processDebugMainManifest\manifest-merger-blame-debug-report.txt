1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.macrotracker.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\MyCal\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\MyCal\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.CAMERA" />
13-->C:\MyCal\app\src\main\AndroidManifest.xml:7:5-65
13-->C:\MyCal\app\src\main\AndroidManifest.xml:7:22-62
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->C:\MyCal\app\src\main\AndroidManifest.xml:8:5-71
14-->C:\MyCal\app\src\main\AndroidManifest.xml:8:22-68
15    <uses-permission
15-->C:\MyCal\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\MyCal\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\MyCal\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\MyCal\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\MyCal\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->C:\MyCal\app\src\main\AndroidManifest.xml:12:9-35
21
22    <!-- Camera features -->
23    <uses-feature
23-->C:\MyCal\app\src\main\AndroidManifest.xml:15:5-17:36
24        android:name="android.hardware.camera"
24-->C:\MyCal\app\src\main\AndroidManifest.xml:16:9-47
25        android:required="false" />
25-->C:\MyCal\app\src\main\AndroidManifest.xml:17:9-33
26    <uses-feature
26-->C:\MyCal\app\src\main\AndroidManifest.xml:18:5-20:36
27        android:name="android.hardware.camera.autofocus"
27-->C:\MyCal\app\src\main\AndroidManifest.xml:19:9-57
28        android:required="false" />
28-->C:\MyCal\app\src\main\AndroidManifest.xml:20:9-33
29    <uses-feature
29-->C:\MyCal\app\src\main\AndroidManifest.xml:21:5-23:36
30        android:name="android.hardware.microphone"
30-->C:\MyCal\app\src\main\AndroidManifest.xml:22:9-51
31        android:required="false" />
31-->C:\MyCal\app\src\main\AndroidManifest.xml:23:9-33
32
33    <permission
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.macrotracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.macrotracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->C:\MyCal\app\src\main\AndroidManifest.xml:25:5-58:19
40        android:name="com.macrotracker.app.MacroTrackerApplication"
40-->C:\MyCal\app\src\main\AndroidManifest.xml:26:9-48
41        android:allowBackup="true"
41-->C:\MyCal\app\src\main\AndroidManifest.xml:27:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8794f68105cf4d13d6ceb8339e99e2bd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->C:\MyCal\app\src\main\AndroidManifest.xml:28:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->C:\MyCal\app\src\main\AndroidManifest.xml:29:9-54
47        android:icon="@mipmap/ic_launcher"
47-->C:\MyCal\app\src\main\AndroidManifest.xml:30:9-43
48        android:label="@string/app_name"
48-->C:\MyCal\app\src\main\AndroidManifest.xml:31:9-41
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->C:\MyCal\app\src\main\AndroidManifest.xml:32:9-54
50        android:supportsRtl="true"
50-->C:\MyCal\app\src\main\AndroidManifest.xml:33:9-35
51        android:testOnly="true"
52        android:theme="@style/Theme.MacroTracker" >
52-->C:\MyCal\app\src\main\AndroidManifest.xml:34:9-50
53        <activity
53-->C:\MyCal\app\src\main\AndroidManifest.xml:37:9-45:20
54            android:name="com.macrotracker.app.MainActivity"
54-->C:\MyCal\app\src\main\AndroidManifest.xml:38:13-41
55            android:exported="true"
55-->C:\MyCal\app\src\main\AndroidManifest.xml:39:13-36
56            android:theme="@style/Theme.MacroTracker" >
56-->C:\MyCal\app\src\main\AndroidManifest.xml:40:13-54
57            <intent-filter>
57-->C:\MyCal\app\src\main\AndroidManifest.xml:41:13-44:29
58                <action android:name="android.intent.action.MAIN" />
58-->C:\MyCal\app\src\main\AndroidManifest.xml:42:17-69
58-->C:\MyCal\app\src\main\AndroidManifest.xml:42:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->C:\MyCal\app\src\main\AndroidManifest.xml:43:17-77
60-->C:\MyCal\app\src\main\AndroidManifest.xml:43:27-74
61            </intent-filter>
62        </activity>
63
64        <!-- File provider for camera -->
65        <provider
66            android:name="androidx.core.content.FileProvider"
66-->C:\MyCal\app\src\main\AndroidManifest.xml:49:13-62
67            android:authorities="com.macrotracker.app.fileprovider"
67-->C:\MyCal\app\src\main\AndroidManifest.xml:50:13-64
68            android:exported="false"
68-->C:\MyCal\app\src\main\AndroidManifest.xml:51:13-37
69            android:grantUriPermissions="true" >
69-->C:\MyCal\app\src\main\AndroidManifest.xml:52:13-47
70            <meta-data
70-->C:\MyCal\app\src\main\AndroidManifest.xml:53:13-55:54
71                android:name="android.support.FILE_PROVIDER_PATHS"
71-->C:\MyCal\app\src\main\AndroidManifest.xml:54:17-67
72                android:resource="@xml/file_paths" />
72-->C:\MyCal\app\src\main\AndroidManifest.xml:55:17-51
73        </provider>
74
75        <!--
76        Service for holding metadata. Cannot be instantiated.
77        Metadata will be merged from other manifests.
78        -->
79        <service
79-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:29:9-33:78
80            android:name="androidx.camera.core.impl.MetadataHolderService"
80-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:30:13-75
81            android:enabled="false"
81-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:31:13-36
82            android:exported="false" >
82-->[androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\097609bc73880f975a3d15eaac2b5f76\transformed\camera-core-1.4.2\AndroidManifest.xml:32:13-37
83            <meta-data
83-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
84                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
84-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
85                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
85-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8c812767ec239b52cac5afe12fe8fb6\transformed\camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
86        </service>
87
88        <activity
88-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
89            android:name="androidx.compose.ui.tooling.PreviewActivity"
89-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
90            android:exported="true" />
90-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4b6036dd5d34237d1a29170612af3a\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
91        <activity
91-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
92            android:name="androidx.activity.ComponentActivity"
92-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
93            android:exported="true"
93-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
94            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
94-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b806e717f2917a0085ce3c057c2fc46\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
95
96        <uses-library
96-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
97            android:name="androidx.window.extensions"
97-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
98            android:required="false" />
98-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
99        <uses-library
99-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
100            android:name="androidx.window.sidecar"
100-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
101            android:required="false" />
101-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e2f5caf83fa23770155ab259872153e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
102
103        <provider
103-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
104            android:name="androidx.startup.InitializationProvider"
104-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
105            android:authorities="com.macrotracker.app.androidx-startup"
105-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
106            android:exported="false" >
106-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
107            <meta-data
107-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.emoji2.text.EmojiCompatInitializer"
108-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
109                android:value="androidx.startup" />
109-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8e22ee88a68010e704b5ec810a00802\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
111-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
112                android:value="androidx.startup" />
112-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\645a1c7fc6da9478c55e38a87c5df71e\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:16:13-18:52
114                android:name="okhttp3.internal.platform.PlatformInitializer"
114-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:17:17-77
115                android:value="androidx.startup" />
115-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f016907a73dbcdb44c2f4ff1388b898\transformed\okhttp-release\AndroidManifest.xml:18:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <service
121-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
122            android:name="androidx.room.MultiInstanceInvalidationService"
122-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
123            android:directBootAware="true"
123-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
124            android:exported="false" />
124-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6470a080cbc262907505462207b86c5d\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4868358e994ea542e1c0bef06f97e69\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
