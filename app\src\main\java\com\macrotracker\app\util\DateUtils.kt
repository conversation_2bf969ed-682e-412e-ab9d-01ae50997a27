package com.macrotracker.app.util

import java.text.SimpleDateFormat
import java.util.*

object DateUtils {
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val displayDateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    fun formatDateForStorage(date: Date): String {
        return dateFormat.format(date)
    }
    
    fun formatDateForDisplay(date: Date): String {
        return displayDateFormat.format(date)
    }
    
    fun formatTimeForDisplay(date: Date): String {
        return timeFormat.format(date)
    }
    
    fun getTodayDate(): Date {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.time
    }
    
    fun isToday(date: Date): Boolean {
        val today = getTodayDate()
        val calendar = Calendar.getInstance()
        
        calendar.time = today
        val todayDay = calendar.get(Calendar.DAY_OF_YEAR)
        val todayYear = calendar.get(Calendar.YEAR)
        
        calendar.time = date
        val dateDay = calendar.get(Calendar.DAY_OF_YEAR)
        val dateYear = calendar.get(Calendar.YEAR)
        
        return todayDay == dateDay && todayYear == dateYear
    }
}
