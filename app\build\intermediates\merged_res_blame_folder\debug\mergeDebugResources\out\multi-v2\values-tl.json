{"logs": [{"outputFile": "com.macrotracker.app-mergeDebugResources-2:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831512154db5267262fed228477d4e6b\\transformed\\material3-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4821,4910,4999,5106,5186,5270,5370,5474,5574,5680,5768,5880,5985,6095,6214,6294,6401", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4816,4905,4994,5101,5181,5265,5365,5469,5569,5675,5763,5875,5980,6090,6209,6289,6396,6491"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5641,5765,5887,6005,6126,6225,6325,6442,6589,6716,6866,6951,7050,7145,7243,7364,7502,7606,7753,7901,8048,8218,8356,8479,8604,8729,8825,8924,9049,9184,9291,9395,9508,9653,9802,9918,10024,10100,10200,10297,10407,10496,10585,10692,10772,10856,10956,11060,11160,11266,11354,11466,11571,11681,11800,11880,11987", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "5760,5882,6000,6121,6220,6320,6437,6584,6711,6861,6946,7045,7140,7238,7359,7497,7601,7748,7896,8043,8213,8351,8474,8599,8724,8820,8919,9044,9179,9286,9390,9503,9648,9797,9913,10019,10095,10195,10292,10402,10491,10580,10687,10767,10851,10951,11055,11155,11261,11349,11461,11566,11676,11795,11875,11982,12077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5378ea3a25a7f71addffda9b7a578b53\\transformed\\navigation-ui-2.9.3\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16959,17073", "endColumns": "113,122", "endOffsets": "17068,17191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00d4f69db0bff584c09211a0d3ca791f\\transformed\\material-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13077", "endColumns": "88", "endOffsets": "13161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8794f68105cf4d13d6ceb8339e99e2bd\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3582,3679,3781,3882,3979,4086,4194,18346", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3674,3776,3877,3974,4081,4189,4311,18442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c9b3062a3a9baebe8ed60ea9c7199b7\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,280,366,463,565,655,737,829,921,1005,1092,1178,1249,1334,1417,1494,1569,1647,1713", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,84,82,76,74,77,65,126", "endOffsets": "275,361,458,560,650,732,824,916,1000,1087,1173,1244,1329,1412,1489,1564,1642,1708,1835"}, "to": {"startLines": "49,50,51,52,53,58,59,179,180,182,183,187,189,190,191,192,194,195,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4642,4741,4827,4924,5026,5408,5490,17196,17288,17457,17544,17880,18026,18111,18194,18271,18447,18525,18591", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,84,82,76,74,77,65,126", "endOffsets": "4736,4822,4919,5021,5111,5485,5577,17283,17367,17539,17625,17946,18106,18189,18266,18341,18520,18586,18713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6511df1e446016865fae0e249649412\\transformed\\material-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1103,1168,1267,1335,1394,1483,1551,1618,1681,1756,1824,1878,1998,2056,2118,2172,2247,2389,2479,2557,2651,2734,2819,2964,3048,3131,3277,3373,3450,3508,3559,3625,3699,3777,3848,3934,4008,4087,4160,4232,4348,4452,4525,4624,4724,4798,4873,4980,5032,5121,5188,5279,5373,5435,5499,5562,5632,5751,5856,5965,6065,6127,6182,6267,6354,6432", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "275,359,439,525,622,712,817,953,1038,1098,1163,1262,1330,1389,1478,1546,1613,1676,1751,1819,1873,1993,2051,2113,2167,2242,2384,2474,2552,2646,2729,2814,2959,3043,3126,3272,3368,3445,3503,3554,3620,3694,3772,3843,3929,4003,4082,4155,4227,4343,4447,4520,4619,4719,4793,4868,4975,5027,5116,5183,5274,5368,5430,5494,5557,5627,5746,5851,5960,6060,6122,6177,6262,6349,6427,6502"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3145,3229,3309,3395,3492,4316,4421,4557,5116,5176,5241,5340,5582,12082,12171,12239,12306,12369,12444,12512,12566,12686,12744,12806,12860,12935,13166,13256,13334,13428,13511,13596,13741,13825,13908,14054,14150,14227,14285,14336,14402,14476,14554,14625,14711,14785,14864,14937,15009,15125,15229,15302,15401,15501,15575,15650,15757,15809,15898,15965,16056,16150,16212,16276,16339,16409,16528,16633,16742,16842,16904,17372,17715,17802,17951", "endLines": "5,34,35,36,37,38,46,47,48,54,55,56,57,60,118,119,120,121,122,123,124,125,126,127,128,129,130,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,181,185,186,188", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "325,3224,3304,3390,3487,3577,4416,4552,4637,5171,5236,5335,5403,5636,12166,12234,12301,12364,12439,12507,12561,12681,12739,12801,12855,12930,13072,13251,13329,13423,13506,13591,13736,13820,13903,14049,14145,14222,14280,14331,14397,14471,14549,14620,14706,14780,14859,14932,15004,15120,15224,15297,15396,15496,15570,15645,15752,15804,15893,15960,16051,16145,16207,16271,16334,16404,16523,16628,16737,16837,16899,16954,17452,17797,17875,18021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e2ee5ef775fc5fd4089dfad5d6aab731\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,17630", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,17710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f09420083b708841bdec860b0e3e3c1b\\transformed\\foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,91", "endOffsets": "125,211,303"}, "to": {"startLines": "33,197,198", "startColumns": "4,4,4", "startOffsets": "3070,18718,18804", "endColumns": "74,85,91", "endOffsets": "3140,18799,18891"}}]}]}