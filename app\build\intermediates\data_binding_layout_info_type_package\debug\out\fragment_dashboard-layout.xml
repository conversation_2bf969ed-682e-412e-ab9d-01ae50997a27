<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_dashboard" modulePackage="com.macrotracker.app" filePath="app\src\main\res\layout\fragment_dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_dashboard_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="198" endOffset="12"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="21" startOffset="8" endLine="26" endOffset="39"/></Target><Target id="@+id/calories_progress" view="ProgressBar"><Expressions/><location startLine="48" startOffset="16" endLine="54" endOffset="66"/></Target><Target id="@+id/calories_text" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="62" endOffset="51"/></Target><Target id="@+id/protein_progress" view="ProgressBar"><Expressions/><location startLine="88" startOffset="16" endLine="94" endOffset="65"/></Target><Target id="@+id/protein_text" view="TextView"><Expressions/><location startLine="96" startOffset="16" endLine="102" endOffset="46"/></Target><Target id="@+id/carbs_progress" view="ProgressBar"><Expressions/><location startLine="128" startOffset="16" endLine="134" endOffset="63"/></Target><Target id="@+id/carbs_text" view="TextView"><Expressions/><location startLine="136" startOffset="16" endLine="142" endOffset="46"/></Target><Target id="@+id/fat_progress" view="ProgressBar"><Expressions/><location startLine="168" startOffset="16" endLine="174" endOffset="61"/></Target><Target id="@+id/fat_text" view="TextView"><Expressions/><location startLine="176" startOffset="16" endLine="182" endOffset="44"/></Target><Target id="@+id/refresh_button" view="Button"><Expressions/><location startLine="188" startOffset="8" endLine="194" endOffset="36"/></Target></Targets></Layout>